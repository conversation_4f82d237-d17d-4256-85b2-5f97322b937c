/ Header Record For PersistentHashMapValueStorageN (com.tqhit.adlib.sdk.AdLibHiltApplication$androidx.lifecycle.LifecycleObserver. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity) (androidx.appcompat.app.AppCompatActivity/ .com.tqhit.battery.one.base.LocaleAwareActivity/ .com.tqhit.battery.one.base.LocaleAwareActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity/ .com.tqhit.battery.one.base.LocaleAwareActivity' &androidx.viewpager.widget.PagerAdapterQ $com.applovin.mediation.MaxAdListener+com.applovin.mediation.MaxAdRevenueListener, +com.applovin.mediation.MaxAdRevenueListenerQ $com.applovin.mediation.MaxAdListener+com.applovin.mediation.MaxAdRevenueListener, +com.applovin.mediation.MaxAdRevenueListenerY ,com.applovin.mediation.MaxRewardedAdListener+com.applovin.mediation.MaxAdRevenueListener. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity android.widget.ProgressBar, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback android.app.DialogC Bcom.tqhit.battery.one.features.stats.charge.cache.StatsChargeCache. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment androidx.lifecycle.ViewModelM Lcom.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepositoryQ Pcom.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider android.app.ServiceI Hcom.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCacheI Hcom.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache, +androidx.lifecycle.DefaultLifecycleObserver kotlin.EnumG Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResultG Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResultG Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult kotlin.Enum kotlin.Enum androidx.fragment.app.Fragment androidx.lifecycle.ViewModel android.app.Service> =com.tqhit.battery.one.features.stats.health.cache.HealthCache kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModelH Gcom.tqhit.battery.one.features.stats.health.repository.HealthRepository android.app.Service kotlin.Enum. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder9 8androidx.recyclerview.widget.RecyclerView.ItemDecoration2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder kotlin.Enum kotlin.EnumA @com.tqhit.battery.one.fragment.main.animation.data.PreloadResultA @com.tqhit.battery.one.fragment.main.animation.data.PreloadResultA @com.tqhit.battery.one.fragment.main.animation.data.PreloadResultJ Icom.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadResultJ Icom.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadResultJ Icom.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadResult. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallbackC Bcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionUiStateC Bcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionUiStateC Bcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionUiStateC Bcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionUiStateA @com.tqhit.battery.one.fragment.onboarding.LanguageSelectionEventA @com.tqhit.battery.one.fragment.onboarding.LanguageSelectionEventA @com.tqhit.battery.one.fragment.onboarding.LanguageSelectionEventA @com.tqhit.battery.one.fragment.onboarding.LanguageSelectionEvent androidx.lifecycle.ViewModel3 2com.tqhit.battery.one.manager.graph.HistoryManager3 2com.tqhit.battery.one.manager.graph.HistoryManager2 1com.tqhit.battery.one.repository.PreloadingResult2 1com.tqhit.battery.one.repository.PreloadingResult2 1com.tqhit.battery.one.repository.PreloadingResult2 1com.tqhit.battery.one.repository.PreloadingResult2 1com.tqhit.battery.one.repository.PreloadingResult2 1com.tqhit.battery.one.repository.PreloadingResult; :com.tqhit.battery.one.repository.ThumbnailPreloadingStatus; :com.tqhit.battery.one.repository.ThumbnailPreloadingStatus; :com.tqhit.battery.one.repository.ThumbnailPreloadingStatus; :com.tqhit.battery.one.repository.ThumbnailPreloadingStatus; :com.tqhit.battery.one.repository.ThumbnailPreloadingResult; :com.tqhit.battery.one.repository.ThumbnailPreloadingResult; :com.tqhit.battery.one.repository.ThumbnailPreloadingResult; :com.tqhit.battery.one.repository.ThumbnailPreloadingResult; :com.tqhit.battery.one.repository.ThumbnailPreloadingResult android.app.Service androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivityO Ncom.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable kotlin.Enum. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragmentK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent androidx.lifecycle.ViewModel/ .com.tqhit.battery.one.base.LocaleAwareActivity3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolderN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventV Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEventV Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEventV Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEventV Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEventV Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEventV Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent^ ]com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigationEvent^ ]com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigationEvent^ ]com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigationEvent^ ]com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigationEvent^ ]com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigationEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventY Xcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ErrorEventY Xcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ErrorEventY Xcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ErrorEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventV Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UIEventV Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UIEventV Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UIEventV Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UIEventV Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UIEvent kotlin.EnumN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventZ Ycom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SystemEventZ Ycom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SystemEventZ Ycom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SystemEventZ Ycom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SystemEventZ Ycom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SystemEvent kotlin.EnumJ Icom.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEventJ Icom.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEventJ Icom.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEventJ Icom.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEventJ Icom.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent androidx.lifecycle.ViewModel. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder9 8androidx.recyclerview.widget.RecyclerView.ItemDecoration2 1android.accessibilityservice.AccessibilityService android.view.ViewR Qcom.tqhit.battery.one.fragment.main.others.adapter.OthersAdapter.OthersViewHolderR Qcom.tqhit.battery.one.fragment.main.others.adapter.OthersAdapter.OthersViewHolder? >com.tqhit.battery.one.fragment.main.others.data.OthersItemData? >com.tqhit.battery.one.fragment.main.others.data.OthersItemData" !androidx.cardview.widget.CardViewN (com.tqhit.adlib.sdk.AdLibHiltApplication$androidx.lifecycle.LifecycleObserver. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity) (androidx.appcompat.app.AppCompatActivity/ .com.tqhit.battery.one.base.LocaleAwareActivity/ .com.tqhit.battery.one.base.LocaleAwareActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity/ .com.tqhit.battery.one.base.LocaleAwareActivityQ $com.applovin.mediation.MaxAdListener+com.applovin.mediation.MaxAdRevenueListenerW +com.applovin.mediation.MaxAdRevenueListener*com.applovin.mediation.MaxAdViewAdListenerQ $com.applovin.mediation.MaxAdListener+com.applovin.mediation.MaxAdRevenueListener, +com.applovin.mediation.MaxAdRevenueListenerY ,com.applovin.mediation.MaxRewardedAdListener+com.applovin.mediation.MaxAdRevenueListener, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback android.app.Dialog. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment android.app.Service androidx.fragment.app.Fragment android.app.Service android.app.Service kotlin.Enum. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder9 8androidx.recyclerview.widget.RecyclerView.ItemDecoration2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.lifecycle.ViewModel/ .com.tqhit.battery.one.base.LocaleAwareActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment/ .com.tqhit.battery.one.base.LocaleAwareActivity/ .com.tqhit.battery.one.base.LocaleAwareActivity