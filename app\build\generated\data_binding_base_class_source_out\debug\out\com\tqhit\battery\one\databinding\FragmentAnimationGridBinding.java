// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentAnimationGridBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView animationInfo;

  @NonNull
  public final RecyclerView animationRecyclerView;

  @NonNull
  public final TextView animationTitle;

  @NonNull
  public final RecyclerView categoryRecyclerView;

  private FragmentAnimationGridBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageView animationInfo, @NonNull RecyclerView animationRecyclerView,
      @NonNull TextView animationTitle, @NonNull RecyclerView categoryRecyclerView) {
    this.rootView = rootView;
    this.animationInfo = animationInfo;
    this.animationRecyclerView = animationRecyclerView;
    this.animationTitle = animationTitle;
    this.categoryRecyclerView = categoryRecyclerView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentAnimationGridBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentAnimationGridBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_animation_grid, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentAnimationGridBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.animation_info;
      ImageView animationInfo = ViewBindings.findChildViewById(rootView, id);
      if (animationInfo == null) {
        break missingId;
      }

      id = R.id.animationRecyclerView;
      RecyclerView animationRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (animationRecyclerView == null) {
        break missingId;
      }

      id = R.id.animation_title;
      TextView animationTitle = ViewBindings.findChildViewById(rootView, id);
      if (animationTitle == null) {
        break missingId;
      }

      id = R.id.categoryRecyclerView;
      RecyclerView categoryRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (categoryRecyclerView == null) {
        break missingId;
      }

      return new FragmentAnimationGridBinding((ConstraintLayout) rootView, animationInfo,
          animationRecyclerView, animationTitle, categoryRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
