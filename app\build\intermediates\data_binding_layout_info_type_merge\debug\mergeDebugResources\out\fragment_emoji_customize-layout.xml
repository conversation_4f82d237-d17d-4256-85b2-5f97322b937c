<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_emoji_customize" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_emoji_customize.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_emoji_customize_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="532" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="30" startOffset="8" endLine="514" endOffset="22"/></Target><Target id="@+id/backNavigation" tag="binding_1" include="layout_back_navigation"><Expressions/><location startLine="40" startOffset="12" endLine="45" endOffset="51"/></Target><Target id="@+id/bannerAdContainer" view="FrameLayout"><Expressions/><location startLine="8" startOffset="4" endLine="16" endOffset="55"/></Target><Target id="@+id/scrollView" view="ScrollView"><Expressions/><location startLine="19" startOffset="4" endLine="515" endOffset="16"/></Target><Target id="@+id/customizeTitle" view="TextView"><Expressions/><location startLine="54" startOffset="16" endLine="63" endOffset="46"/></Target><Target id="@+id/customizeInfo" view="ImageView"><Expressions/><location startLine="65" startOffset="16" endLine="72" endOffset="50"/></Target><Target id="@+id/globalToggleSwitch" view="Switch"><Expressions/><location startLine="109" startOffset="16" endLine="113" endOffset="55"/></Target><Target id="@+id/previewContainer" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="135" startOffset="16" endLine="184" endOffset="67"/></Target><Target id="@+id/previewContent" view="RelativeLayout"><Expressions/><location startLine="143" startOffset="20" endLine="183" endOffset="36"/></Target><Target id="@+id/previewBattery" view="ImageView"><Expressions/><location startLine="153" startOffset="24" endLine="160" endOffset="66"/></Target><Target id="@+id/previewEmoji" view="ImageView"><Expressions/><location startLine="164" startOffset="24" endLine="171" endOffset="64"/></Target><Target id="@+id/previewPercentage" view="TextView"><Expressions/><location startLine="174" startOffset="24" endLine="182" endOffset="53"/></Target><Target id="@+id/previewLevelSlider" view="SeekBar"><Expressions/><location startLine="201" startOffset="20" endLine="207" endOffset="47"/></Target><Target id="@+id/previewLevelText" view="TextView"><Expressions/><location startLine="209" startOffset="20" endLine="218" endOffset="50"/></Target><Target id="@+id/batteryStylesRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="241" startOffset="16" endLine="248" endOffset="90"/></Target><Target id="@+id/emojiStylesRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="260" startOffset="16" endLine="266" endOffset="90"/></Target><Target id="@+id/showEmojiSwitch" view="Switch"><Expressions/><location startLine="303" startOffset="20" endLine="307" endOffset="48"/></Target><Target id="@+id/showPercentageSwitch" view="Switch"><Expressions/><location startLine="325" startOffset="20" endLine="329" endOffset="48"/></Target><Target id="@+id/fontSizeSlider" view="SeekBar"><Expressions/><location startLine="376" startOffset="20" endLine="382" endOffset="46"/></Target><Target id="@+id/fontSizeValue" view="TextView"><Expressions/><location startLine="392" startOffset="20" endLine="401" endOffset="50"/></Target><Target id="@+id/emojiScaleSlider" view="SeekBar"><Expressions/><location startLine="428" startOffset="20" endLine="434" endOffset="46"/></Target><Target id="@+id/emojiScaleValue" view="TextView"><Expressions/><location startLine="444" startOffset="20" endLine="453" endOffset="50"/></Target><Target id="@+id/loadingContainer" view="FrameLayout"><Expressions/><location startLine="458" startOffset="12" endLine="478" endOffset="25"/></Target><Target id="@+id/errorContainer" view="LinearLayout"><Expressions/><location startLine="481" startOffset="12" endLine="512" endOffset="26"/></Target><Target id="@+id/errorMessage" view="TextView"><Expressions/><location startLine="496" startOffset="16" endLine="504" endOffset="45"/></Target><Target id="@+id/retryButton" view="Button"><Expressions/><location startLine="506" startOffset="16" endLine="511" endOffset="50"/></Target><Target id="@+id/applyButton" view="Button"><Expressions/><location startLine="518" startOffset="4" endLine="530" endOffset="55"/></Target></Targets></Layout>