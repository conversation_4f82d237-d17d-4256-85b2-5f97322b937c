// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentOthersBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout antiThiefBlock;

  @NonNull
  public final ImageView antiThiefInfo;

  @NonNull
  public final RecyclerView othersRecyclerView;

  @NonNull
  public final ScrollView scrollView;

  @NonNull
  public final ConstraintLayout switchAntiThiefBlock;

  @NonNull
  public final TextView switchAntiThiefTitle;

  @NonNull
  public final SwitchCompat switchEnableAntiThief;

  private FragmentOthersBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout antiThiefBlock, @NonNull ImageView antiThiefInfo,
      @NonNull RecyclerView othersRecyclerView, @NonNull ScrollView scrollView,
      @NonNull ConstraintLayout switchAntiThiefBlock, @NonNull TextView switchAntiThiefTitle,
      @NonNull SwitchCompat switchEnableAntiThief) {
    this.rootView = rootView;
    this.antiThiefBlock = antiThiefBlock;
    this.antiThiefInfo = antiThiefInfo;
    this.othersRecyclerView = othersRecyclerView;
    this.scrollView = scrollView;
    this.switchAntiThiefBlock = switchAntiThiefBlock;
    this.switchAntiThiefTitle = switchAntiThiefTitle;
    this.switchEnableAntiThief = switchEnableAntiThief;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentOthersBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentOthersBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_others, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentOthersBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.anti_thief_block;
      ConstraintLayout antiThiefBlock = ViewBindings.findChildViewById(rootView, id);
      if (antiThiefBlock == null) {
        break missingId;
      }

      id = R.id.anti_thief_info;
      ImageView antiThiefInfo = ViewBindings.findChildViewById(rootView, id);
      if (antiThiefInfo == null) {
        break missingId;
      }

      id = R.id.othersRecyclerView;
      RecyclerView othersRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (othersRecyclerView == null) {
        break missingId;
      }

      id = R.id.scrollView;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.switch_anti_thief_block;
      ConstraintLayout switchAntiThiefBlock = ViewBindings.findChildViewById(rootView, id);
      if (switchAntiThiefBlock == null) {
        break missingId;
      }

      id = R.id.switch_anti_thief_title;
      TextView switchAntiThiefTitle = ViewBindings.findChildViewById(rootView, id);
      if (switchAntiThiefTitle == null) {
        break missingId;
      }

      id = R.id.switch_enable_anti_thief;
      SwitchCompat switchEnableAntiThief = ViewBindings.findChildViewById(rootView, id);
      if (switchEnableAntiThief == null) {
        break missingId;
      }

      return new FragmentOthersBinding((ConstraintLayout) rootView, antiThiefBlock, antiThiefInfo,
          othersRecyclerView, scrollView, switchAntiThiefBlock, switchAntiThiefTitle,
          switchEnableAntiThief);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
