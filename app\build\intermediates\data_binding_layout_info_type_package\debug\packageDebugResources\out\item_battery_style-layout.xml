<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_battery_style" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_battery_style.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.tqhit.battery.one.ui.custom.SquareCardView"><Targets><Target tag="layout/item_battery_style_0" view="com.tqhit.battery.one.ui.custom.SquareCardView"><Expressions/><location startLine="1" startOffset="0" endLine="133" endOffset="48"/></Target><Target id="@+id/styleImage" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="14" startOffset="4" endLine="25" endOffset="50"/></Target><Target id="@+id/shimmerLayout" view="com.facebook.shimmer.ShimmerFrameLayout"><Expressions/><location startLine="28" startOffset="4" endLine="36" endOffset="57"/></Target><Target id="@+id/lockBtn" view="TextView"><Expressions/><location startLine="39" startOffset="4" endLine="54" endOffset="50"/></Target><Target id="@+id/styleName" view="TextView"><Expressions/><location startLine="57" startOffset="4" endLine="74" endOffset="62"/></Target><Target id="@+id/popularIndicator" view="ImageView"><Expressions/><location startLine="77" startOffset="4" endLine="86" endOffset="59"/></Target><Target id="@+id/categoryBadge" view="TextView"><Expressions/><location startLine="89" startOffset="4" endLine="105" endOffset="63"/></Target><Target id="@+id/selectionOverlay" view="View"><Expressions/><location startLine="108" startOffset="4" endLine="117" endOffset="57"/></Target><Target id="@+id/selectionCheckMark" view="ImageView"><Expressions/><location startLine="120" startOffset="4" endLine="129" endOffset="65"/></Target></Targets></Layout>