package com.tqhit.battery.one.features.emoji.presentation.overlay;

import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository;
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class EmojiBatteryAccessibilityService_MembersInjector implements MembersInjector<EmojiBatteryAccessibilityService> {
  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  private final Provider<CustomizationRepository> customizationRepositoryProvider;

  public EmojiBatteryAccessibilityService_MembersInjector(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<CustomizationRepository> customizationRepositoryProvider) {
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
    this.customizationRepositoryProvider = customizationRepositoryProvider;
  }

  public static MembersInjector<EmojiBatteryAccessibilityService> create(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<CustomizationRepository> customizationRepositoryProvider) {
    return new EmojiBatteryAccessibilityService_MembersInjector(coreBatteryStatsProvider, customizationRepositoryProvider);
  }

  @Override
  public void injectMembers(EmojiBatteryAccessibilityService instance) {
    injectCoreBatteryStatsProvider(instance, coreBatteryStatsProvider.get());
    injectCustomizationRepository(instance, customizationRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService.coreBatteryStatsProvider")
  public static void injectCoreBatteryStatsProvider(EmojiBatteryAccessibilityService instance,
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    instance.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService.customizationRepository")
  public static void injectCustomizationRepository(EmojiBatteryAccessibilityService instance,
      CustomizationRepository customizationRepository) {
    instance.customizationRepository = customizationRepository;
  }
}
