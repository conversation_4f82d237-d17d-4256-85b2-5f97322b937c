package com.tqhit.battery.one.activity.main.handlers;

import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager;
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper;
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper;
import com.tqhit.battery.one.service.ChargingOverlayServiceHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ServiceManager_Factory implements Factory<ServiceManager> {
  private final Provider<UnifiedBatteryNotificationServiceHelper> unifiedBatteryNotificationServiceHelperProvider;

  private final Provider<EnhancedDischargeTimerServiceHelper> enhancedDischargeTimerServiceHelperProvider;

  private final Provider<ChargingOverlayServiceHelper> chargingOverlayServiceHelperProvider;

  private final Provider<AppLifecycleManager> appLifecycleManagerProvider;

  public ServiceManager_Factory(
      Provider<UnifiedBatteryNotificationServiceHelper> unifiedBatteryNotificationServiceHelperProvider,
      Provider<EnhancedDischargeTimerServiceHelper> enhancedDischargeTimerServiceHelperProvider,
      Provider<ChargingOverlayServiceHelper> chargingOverlayServiceHelperProvider,
      Provider<AppLifecycleManager> appLifecycleManagerProvider) {
    this.unifiedBatteryNotificationServiceHelperProvider = unifiedBatteryNotificationServiceHelperProvider;
    this.enhancedDischargeTimerServiceHelperProvider = enhancedDischargeTimerServiceHelperProvider;
    this.chargingOverlayServiceHelperProvider = chargingOverlayServiceHelperProvider;
    this.appLifecycleManagerProvider = appLifecycleManagerProvider;
  }

  @Override
  public ServiceManager get() {
    return newInstance(unifiedBatteryNotificationServiceHelperProvider.get(), enhancedDischargeTimerServiceHelperProvider.get(), chargingOverlayServiceHelperProvider.get(), appLifecycleManagerProvider.get());
  }

  public static ServiceManager_Factory create(
      Provider<UnifiedBatteryNotificationServiceHelper> unifiedBatteryNotificationServiceHelperProvider,
      Provider<EnhancedDischargeTimerServiceHelper> enhancedDischargeTimerServiceHelperProvider,
      Provider<ChargingOverlayServiceHelper> chargingOverlayServiceHelperProvider,
      Provider<AppLifecycleManager> appLifecycleManagerProvider) {
    return new ServiceManager_Factory(unifiedBatteryNotificationServiceHelperProvider, enhancedDischargeTimerServiceHelperProvider, chargingOverlayServiceHelperProvider, appLifecycleManagerProvider);
  }

  public static ServiceManager newInstance(
      UnifiedBatteryNotificationServiceHelper unifiedBatteryNotificationServiceHelper,
      EnhancedDischargeTimerServiceHelper enhancedDischargeTimerServiceHelper,
      ChargingOverlayServiceHelper chargingOverlayServiceHelper,
      AppLifecycleManager appLifecycleManager) {
    return new ServiceManager(unifiedBatteryNotificationServiceHelper, enhancedDischargeTimerServiceHelper, chargingOverlayServiceHelper, appLifecycleManager);
  }
}
