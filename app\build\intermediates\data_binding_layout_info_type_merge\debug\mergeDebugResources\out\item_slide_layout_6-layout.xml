<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_slide_layout_6" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_slide_layout_6.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_slide_layout_6_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="233" endOffset="14"/></Target><Target id="@+id/textView4" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="34" endOffset="44"/></Target><Target id="@+id/textView16" view="TextView"><Expressions/><location startLine="35" startOffset="8" endLine="47" endOffset="44"/></Target><Target id="@+id/textView15" view="TextView"><Expressions/><location startLine="48" startOffset="8" endLine="58" endOffset="44"/></Target><Target id="@+id/dontkillmyapp_button" view="Button"><Expressions/><location startLine="67" startOffset="12" endLine="85" endOffset="66"/></Target><Target id="@+id/textView18" view="TextView"><Expressions/><location startLine="87" startOffset="8" endLine="99" endOffset="44"/></Target><Target id="@+id/work_in_background_permission" view="Button"><Expressions/><location startLine="107" startOffset="12" endLine="125" endOffset="66"/></Target><Target id="@+id/autorun_view" view="LinearLayout"><Expressions/><location startLine="127" startOffset="8" endLine="197" endOffset="22"/></Target><Target id="@+id/textView19" view="TextView"><Expressions/><location startLine="133" startOffset="12" endLine="146" endOffset="55"/></Target><Target id="@+id/button_layout" view="RelativeLayout"><Expressions/><location startLine="156" startOffset="16" endLine="195" endOffset="32"/></Target><Target id="@+id/buttonEnable" view="Button"><Expressions/><location startLine="168" startOffset="20" endLine="180" endOffset="74"/></Target><Target id="@+id/text_view_reset_charge" view="TextView"><Expressions/><location startLine="181" startOffset="20" endLine="194" endOffset="56"/></Target><Target id="@+id/next_page" view="LinearLayout"><Expressions/><location startLine="202" startOffset="4" endLine="232" endOffset="18"/></Target><Target id="@+id/swipe_text" view="TextView"><Expressions/><location startLine="216" startOffset="8" endLine="225" endOffset="40"/></Target></Targets></Layout>