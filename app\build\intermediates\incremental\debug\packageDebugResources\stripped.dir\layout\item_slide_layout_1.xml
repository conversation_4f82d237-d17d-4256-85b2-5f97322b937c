<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout  xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <LinearLayout
        android:gravity="center"
        android:orientation="vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/logo"
            android:background="@drawable/logo_start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"/>
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp">
            <TextView
                android:textSize="22sp"
                android:textColor="?attr/black"
                android:gravity="center_horizontal"
                android:id="@+id/textView2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/app_name"/>
            <TextView
                android:textSize="15sp"
                android:textColor="?attr/black"
                android:gravity="center_horizontal"
                android:id="@+id/textView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/by_TJ"/>
        </LinearLayout>
    </LinearLayout>
    <TextView
        android:textSize="22sp"
        android:textColor="@color/black"
        android:gravity="center_horizontal"
        android:id="@+id/textView14"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:layout_centerInParent="true"/>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/lenght_layout"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/main"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="22sp"
                android:textColor="@color/black"
                android:ellipsize="marquee"
                android:gravity="start"
                android:id="@+id/textView4"
                android:focusableInTouchMode="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:layout_marginStart="14dp"
                android:layout_marginEnd="14dp"/>
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/text5"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="7dp"
                android:layout_marginBottom="1dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:layout_marginBottom="7dp"
                    android:text="@string/disconnect_charge"
                    android:layout_marginStart="9dp"
                    android:layout_marginEnd="7dp"/>
            </LinearLayout>
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="7dp"
                android:layout_marginBottom="8dp">
                <LinearLayout
                    android:gravity="center"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:layout_marginStart="10dp"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:layout_marginStart="10dp"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:layout_marginStart="10dp"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:layout_marginStart="10dp"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:layout_marginStart="10dp"/>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp"
            android:elevation="1dp">
            <LinearLayout
                android:orientation="vertical"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:layout_below="@+id/under_graph_temp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:layout_marginBottom="7dp"
                    android:text="@string/design_capacity_text_incorrect"
                    android:layout_marginStart="9dp"
                    android:layout_marginEnd="7dp"/>
            </LinearLayout>
            <RelativeLayout
                android:gravity="center"
                android:id="@+id/reset_session_charge_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="8dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp">
                <TextView
                    android:textSize="14sp"
                    android:textColor="@color/black"
                    android:gravity="center"
                    android:id="@+id/text_view_reset_charge"
                    android:paddingTop="12.5dp"
                    android:paddingBottom="12.5dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/change_design_capacity"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"/>
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_gravity="center"
        android:id="@+id/next_page"
        android:visibility="visible"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="50dp"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="30dp"
        android:layout_alignParentEnd="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:stateListAnimator="@null"
        android:outlineProvider="background">
        <TextView
            android:id="@+id/swipe_text"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_16sdp"
            android:textStyle="bold"
            android:textColor="?attr/black"
            android:textSize="@dimen/_14ssp"
            android:layout_marginEnd="@dimen/_3sdp"
            android:textAllCaps="true"
            android:text="@string/next"/>
        <ImageView
            android:layout_width="@dimen/_12sdp"
            android:layout_height="@dimen/_12sdp"
            android:src="@drawable/ic_strelka"
            android:contentDescription="@string/next"
            android:scaleX="-1"/>
    </LinearLayout>




    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/display"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

</RelativeLayout>
