package com.tqhit.battery.one.features.stats.notifications;

import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class UnifiedBatteryNotificationService_MembersInjector implements MembersInjector<UnifiedBatteryNotificationService> {
  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  public UnifiedBatteryNotificationService_MembersInjector(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<AppRepository> appRepositoryProvider) {
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
    this.appRepositoryProvider = appRepositoryProvider;
  }

  public static MembersInjector<UnifiedBatteryNotificationService> create(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<AppRepository> appRepositoryProvider) {
    return new UnifiedBatteryNotificationService_MembersInjector(coreBatteryStatsProvider, appRepositoryProvider);
  }

  @Override
  public void injectMembers(UnifiedBatteryNotificationService instance) {
    injectCoreBatteryStatsProvider(instance, coreBatteryStatsProvider.get());
    injectAppRepository(instance, appRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.coreBatteryStatsProvider")
  public static void injectCoreBatteryStatsProvider(UnifiedBatteryNotificationService instance,
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    instance.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.appRepository")
  public static void injectAppRepository(UnifiedBatteryNotificationService instance,
      AppRepository appRepository) {
    instance.appRepository = appRepository;
  }
}
