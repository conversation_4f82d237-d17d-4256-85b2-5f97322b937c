package com.tqhit.battery.one.features.emoji.domain.use_case;

import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SaveCustomizationUseCase_Factory implements Factory<SaveCustomizationUseCase> {
  private final Provider<CustomizationRepository> customizationRepositoryProvider;

  public SaveCustomizationUseCase_Factory(
      Provider<CustomizationRepository> customizationRepositoryProvider) {
    this.customizationRepositoryProvider = customizationRepositoryProvider;
  }

  @Override
  public SaveCustomizationUseCase get() {
    return newInstance(customizationRepositoryProvider.get());
  }

  public static SaveCustomizationUseCase_Factory create(
      Provider<CustomizationRepository> customizationRepositoryProvider) {
    return new SaveCustomizationUseCase_Factory(customizationRepositoryProvider);
  }

  public static SaveCustomizationUseCase newInstance(
      CustomizationRepository customizationRepository) {
    return new SaveCustomizationUseCase(customizationRepository);
  }
}
