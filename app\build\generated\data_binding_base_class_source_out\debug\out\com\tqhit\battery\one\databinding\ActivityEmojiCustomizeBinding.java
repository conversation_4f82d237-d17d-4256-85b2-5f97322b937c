// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEmojiCustomizeBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button applyButton;

  @NonNull
  public final FrameLayout bannerAdContainer;

  @NonNull
  public final RecyclerView batteryStylesRecyclerView;

  @NonNull
  public final ImageView customizeInfo;

  @NonNull
  public final TextView customizeSubtitle;

  @NonNull
  public final SeekBar emojiScaleSlider;

  @NonNull
  public final TextView emojiScaleValue;

  @NonNull
  public final RecyclerView emojiStylesRecyclerView;

  @NonNull
  public final LinearLayout errorContainer;

  @NonNull
  public final TextView errorMessage;

  @NonNull
  public final SeekBar fontSizeSlider;

  @NonNull
  public final TextView fontSizeValue;

  @NonNull
  public final Switch globalToggleSwitch;

  @NonNull
  public final FrameLayout loadingContainer;

  @NonNull
  public final ImageView previewBattery;

  @NonNull
  public final ConstraintLayout previewContainer;

  @NonNull
  public final RelativeLayout previewContent;

  @NonNull
  public final ImageView previewEmoji;

  @NonNull
  public final SeekBar previewLevelSlider;

  @NonNull
  public final TextView previewLevelText;

  @NonNull
  public final TextView previewPercentage;

  @NonNull
  public final Button retryButton;

  @NonNull
  public final ScrollView scrollView;

  @NonNull
  public final Switch showEmojiSwitch;

  @NonNull
  public final Switch showPercentageSwitch;

  @NonNull
  public final Toolbar toolbar;

  private ActivityEmojiCustomizeBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button applyButton, @NonNull FrameLayout bannerAdContainer,
      @NonNull RecyclerView batteryStylesRecyclerView, @NonNull ImageView customizeInfo,
      @NonNull TextView customizeSubtitle, @NonNull SeekBar emojiScaleSlider,
      @NonNull TextView emojiScaleValue, @NonNull RecyclerView emojiStylesRecyclerView,
      @NonNull LinearLayout errorContainer, @NonNull TextView errorMessage,
      @NonNull SeekBar fontSizeSlider, @NonNull TextView fontSizeValue,
      @NonNull Switch globalToggleSwitch, @NonNull FrameLayout loadingContainer,
      @NonNull ImageView previewBattery, @NonNull ConstraintLayout previewContainer,
      @NonNull RelativeLayout previewContent, @NonNull ImageView previewEmoji,
      @NonNull SeekBar previewLevelSlider, @NonNull TextView previewLevelText,
      @NonNull TextView previewPercentage, @NonNull Button retryButton,
      @NonNull ScrollView scrollView, @NonNull Switch showEmojiSwitch,
      @NonNull Switch showPercentageSwitch, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.applyButton = applyButton;
    this.bannerAdContainer = bannerAdContainer;
    this.batteryStylesRecyclerView = batteryStylesRecyclerView;
    this.customizeInfo = customizeInfo;
    this.customizeSubtitle = customizeSubtitle;
    this.emojiScaleSlider = emojiScaleSlider;
    this.emojiScaleValue = emojiScaleValue;
    this.emojiStylesRecyclerView = emojiStylesRecyclerView;
    this.errorContainer = errorContainer;
    this.errorMessage = errorMessage;
    this.fontSizeSlider = fontSizeSlider;
    this.fontSizeValue = fontSizeValue;
    this.globalToggleSwitch = globalToggleSwitch;
    this.loadingContainer = loadingContainer;
    this.previewBattery = previewBattery;
    this.previewContainer = previewContainer;
    this.previewContent = previewContent;
    this.previewEmoji = previewEmoji;
    this.previewLevelSlider = previewLevelSlider;
    this.previewLevelText = previewLevelText;
    this.previewPercentage = previewPercentage;
    this.retryButton = retryButton;
    this.scrollView = scrollView;
    this.showEmojiSwitch = showEmojiSwitch;
    this.showPercentageSwitch = showPercentageSwitch;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEmojiCustomizeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEmojiCustomizeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_emoji_customize, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEmojiCustomizeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.applyButton;
      Button applyButton = ViewBindings.findChildViewById(rootView, id);
      if (applyButton == null) {
        break missingId;
      }

      id = R.id.bannerAdContainer;
      FrameLayout bannerAdContainer = ViewBindings.findChildViewById(rootView, id);
      if (bannerAdContainer == null) {
        break missingId;
      }

      id = R.id.batteryStylesRecyclerView;
      RecyclerView batteryStylesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (batteryStylesRecyclerView == null) {
        break missingId;
      }

      id = R.id.customizeInfo;
      ImageView customizeInfo = ViewBindings.findChildViewById(rootView, id);
      if (customizeInfo == null) {
        break missingId;
      }

      id = R.id.customizeSubtitle;
      TextView customizeSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (customizeSubtitle == null) {
        break missingId;
      }

      id = R.id.emojiScaleSlider;
      SeekBar emojiScaleSlider = ViewBindings.findChildViewById(rootView, id);
      if (emojiScaleSlider == null) {
        break missingId;
      }

      id = R.id.emojiScaleValue;
      TextView emojiScaleValue = ViewBindings.findChildViewById(rootView, id);
      if (emojiScaleValue == null) {
        break missingId;
      }

      id = R.id.emojiStylesRecyclerView;
      RecyclerView emojiStylesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (emojiStylesRecyclerView == null) {
        break missingId;
      }

      id = R.id.errorContainer;
      LinearLayout errorContainer = ViewBindings.findChildViewById(rootView, id);
      if (errorContainer == null) {
        break missingId;
      }

      id = R.id.errorMessage;
      TextView errorMessage = ViewBindings.findChildViewById(rootView, id);
      if (errorMessage == null) {
        break missingId;
      }

      id = R.id.fontSizeSlider;
      SeekBar fontSizeSlider = ViewBindings.findChildViewById(rootView, id);
      if (fontSizeSlider == null) {
        break missingId;
      }

      id = R.id.fontSizeValue;
      TextView fontSizeValue = ViewBindings.findChildViewById(rootView, id);
      if (fontSizeValue == null) {
        break missingId;
      }

      id = R.id.globalToggleSwitch;
      Switch globalToggleSwitch = ViewBindings.findChildViewById(rootView, id);
      if (globalToggleSwitch == null) {
        break missingId;
      }

      id = R.id.loadingContainer;
      FrameLayout loadingContainer = ViewBindings.findChildViewById(rootView, id);
      if (loadingContainer == null) {
        break missingId;
      }

      id = R.id.previewBattery;
      ImageView previewBattery = ViewBindings.findChildViewById(rootView, id);
      if (previewBattery == null) {
        break missingId;
      }

      id = R.id.previewContainer;
      ConstraintLayout previewContainer = ViewBindings.findChildViewById(rootView, id);
      if (previewContainer == null) {
        break missingId;
      }

      id = R.id.previewContent;
      RelativeLayout previewContent = ViewBindings.findChildViewById(rootView, id);
      if (previewContent == null) {
        break missingId;
      }

      id = R.id.previewEmoji;
      ImageView previewEmoji = ViewBindings.findChildViewById(rootView, id);
      if (previewEmoji == null) {
        break missingId;
      }

      id = R.id.previewLevelSlider;
      SeekBar previewLevelSlider = ViewBindings.findChildViewById(rootView, id);
      if (previewLevelSlider == null) {
        break missingId;
      }

      id = R.id.previewLevelText;
      TextView previewLevelText = ViewBindings.findChildViewById(rootView, id);
      if (previewLevelText == null) {
        break missingId;
      }

      id = R.id.previewPercentage;
      TextView previewPercentage = ViewBindings.findChildViewById(rootView, id);
      if (previewPercentage == null) {
        break missingId;
      }

      id = R.id.retryButton;
      Button retryButton = ViewBindings.findChildViewById(rootView, id);
      if (retryButton == null) {
        break missingId;
      }

      id = R.id.scrollView;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.showEmojiSwitch;
      Switch showEmojiSwitch = ViewBindings.findChildViewById(rootView, id);
      if (showEmojiSwitch == null) {
        break missingId;
      }

      id = R.id.showPercentageSwitch;
      Switch showPercentageSwitch = ViewBindings.findChildViewById(rootView, id);
      if (showPercentageSwitch == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityEmojiCustomizeBinding((ConstraintLayout) rootView, applyButton,
          bannerAdContainer, batteryStylesRecyclerView, customizeInfo, customizeSubtitle,
          emojiScaleSlider, emojiScaleValue, emojiStylesRecyclerView, errorContainer, errorMessage,
          fontSizeSlider, fontSizeValue, globalToggleSwitch, loadingContainer, previewBattery,
          previewContainer, previewContent, previewEmoji, previewLevelSlider, previewLevelText,
          previewPercentage, retryButton, scrollView, showEmojiSwitch, showPercentageSwitch,
          toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
