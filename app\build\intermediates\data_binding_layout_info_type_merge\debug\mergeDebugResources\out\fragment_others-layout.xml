<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_others" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_others.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_others_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="118" endOffset="51"/></Target><Target id="@+id/scrollView" view="ScrollView"><Expressions/><location startLine="6" startOffset="4" endLine="116" endOffset="12"/></Target><Target id="@+id/othersRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="31" startOffset="12" endLine="41" endOffset="47"/></Target><Target id="@+id/anti_thief_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="44" startOffset="12" endLine="112" endOffset="63"/></Target><Target id="@+id/switch_anti_thief_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="55" startOffset="16" endLine="110" endOffset="67"/></Target><Target id="@+id/switch_anti_thief_title" view="TextView"><Expressions/><location startLine="65" startOffset="20" endLine="85" endOffset="67"/></Target><Target id="@+id/anti_thief_info" view="ImageView"><Expressions/><location startLine="87" startOffset="20" endLine="96" endOffset="67"/></Target><Target id="@+id/switch_enable_anti_thief" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="97" startOffset="20" endLine="109" endOffset="60"/></Target></Targets></Layout>