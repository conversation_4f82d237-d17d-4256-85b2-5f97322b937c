// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSlideLayout4Binding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout chargeUp;

  @NonNull
  public final LinearLayout nextPage;

  @NonNull
  public final RelativeLayout percentLayout;

  @NonNull
  public final TextView swipeText;

  @NonNull
  public final TextView textView4;

  @NonNull
  public final LinearLayout timeNum;

  private ItemSlideLayout4Binding(@NonNull LinearLayout rootView, @NonNull LinearLayout chargeUp,
      @NonNull LinearLayout nextPage, @NonNull RelativeLayout percentLayout,
      @NonNull TextView swipeText, @NonNull TextView textView4, @NonNull LinearLayout timeNum) {
    this.rootView = rootView;
    this.chargeUp = chargeUp;
    this.nextPage = nextPage;
    this.percentLayout = percentLayout;
    this.swipeText = swipeText;
    this.textView4 = textView4;
    this.timeNum = timeNum;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSlideLayout4Binding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSlideLayout4Binding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_slide_layout_4, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSlideLayout4Binding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.charge_up;
      LinearLayout chargeUp = ViewBindings.findChildViewById(rootView, id);
      if (chargeUp == null) {
        break missingId;
      }

      id = R.id.next_page;
      LinearLayout nextPage = ViewBindings.findChildViewById(rootView, id);
      if (nextPage == null) {
        break missingId;
      }

      id = R.id.percent_layout;
      RelativeLayout percentLayout = ViewBindings.findChildViewById(rootView, id);
      if (percentLayout == null) {
        break missingId;
      }

      id = R.id.swipe_text;
      TextView swipeText = ViewBindings.findChildViewById(rootView, id);
      if (swipeText == null) {
        break missingId;
      }

      id = R.id.textView4;
      TextView textView4 = ViewBindings.findChildViewById(rootView, id);
      if (textView4 == null) {
        break missingId;
      }

      id = R.id.time_num;
      LinearLayout timeNum = ViewBindings.findChildViewById(rootView, id);
      if (timeNum == null) {
        break missingId;
      }

      return new ItemSlideLayout4Binding((LinearLayout) rootView, chargeUp, nextPage, percentLayout,
          swipeText, textView4, timeNum);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
