package com.tqhit.battery.one.features.emoji.presentation.gallery;

import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class EmojiBatteryFragment_MembersInjector implements MembersInjector<EmojiBatteryFragment> {
  private final Provider<AppRepository> appRepositoryProvider;

  public EmojiBatteryFragment_MembersInjector(Provider<AppRepository> appRepositoryProvider) {
    this.appRepositoryProvider = appRepositoryProvider;
  }

  public static MembersInjector<EmojiBatteryFragment> create(
      Provider<AppRepository> appRepositoryProvider) {
    return new EmojiBatteryFragment_MembersInjector(appRepositoryProvider);
  }

  @Override
  public void injectMembers(EmojiBatteryFragment instance) {
    injectAppRepository(instance, appRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment.appRepository")
  public static void injectAppRepository(EmojiBatteryFragment instance,
      AppRepository appRepository) {
    instance.appRepository = appRepository;
  }
}
