<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_slide_layout_1" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_slide_layout_1.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/item_slide_layout_1_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="291" endOffset="16"/></Target><Target id="@+id/logo" view="LinearLayout"><Expressions/><location startLine="13" startOffset="8" endLine="20" endOffset="49"/></Target><Target id="@+id/textView2" view="TextView"><Expressions/><location startLine="26" startOffset="12" endLine="33" endOffset="48"/></Target><Target id="@+id/textView" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="45"/></Target><Target id="@+id/textView14" view="TextView"><Expressions/><location startLine="45" startOffset="4" endLine="54" endOffset="45"/></Target><Target id="@+id/lenght_layout" view="LinearLayout"><Expressions/><location startLine="55" startOffset="4" endLine="247" endOffset="18"/></Target><Target id="@+id/main" view="LinearLayout"><Expressions/><location startLine="62" startOffset="8" endLine="190" endOffset="22"/></Target><Target id="@+id/textView4" view="TextView"><Expressions/><location startLine="68" startOffset="12" endLine="81" endOffset="48"/></Target><Target id="@+id/text5" view="LinearLayout"><Expressions/><location startLine="82" startOffset="12" endLine="103" endOffset="26"/></Target><Target id="@+id/reset_session_charge_layout" view="RelativeLayout"><Expressions/><location startLine="223" startOffset="12" endLine="245" endOffset="28"/></Target><Target id="@+id/text_view_reset_charge" view="TextView"><Expressions/><location startLine="231" startOffset="16" endLine="244" endOffset="52"/></Target><Target id="@+id/next_page" view="LinearLayout"><Expressions/><location startLine="248" startOffset="4" endLine="279" endOffset="18"/></Target><Target id="@+id/swipe_text" view="TextView"><Expressions/><location startLine="263" startOffset="8" endLine="272" endOffset="40"/></Target><Target id="@+id/display" view="LinearLayout"><Expressions/><location startLine="284" startOffset="4" endLine="289" endOffset="45"/></Target></Targets></Layout>