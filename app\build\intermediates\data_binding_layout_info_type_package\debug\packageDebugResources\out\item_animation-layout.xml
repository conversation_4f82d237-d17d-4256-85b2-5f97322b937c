<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_animation" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_animation.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_animation_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="93" endOffset="51"/></Target><Target id="@+id/shimmerLayout" view="com.facebook.shimmer.ShimmerFrameLayout"><Expressions/><location startLine="8" startOffset="4" endLine="26" endOffset="45"/></Target><Target id="@+id/cardImage" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="64"/></Target><Target id="@+id/lockBtn" view="TextView"><Expressions/><location startLine="27" startOffset="4" endLine="42" endOffset="50"/></Target><Target id="@+id/apply_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="44" startOffset="4" endLine="92" endOffset="55"/></Target><Target id="@+id/applyButton" view="Button"><Expressions/><location startLine="54" startOffset="8" endLine="63" endOffset="62"/></Target><Target id="@+id/icon_ad" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="74" startOffset="12" endLine="80" endOffset="47"/></Target><Target id="@+id/text_btn" view="TextView"><Expressions/><location startLine="81" startOffset="12" endLine="90" endOffset="55"/></Target></Targets></Layout>