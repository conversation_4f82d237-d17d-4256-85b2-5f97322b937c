package com.tqhit.battery.one.features.emoji.presentation.customize

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.model.EmojiItem

import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
import com.tqhit.battery.one.features.emoji.data.service.EmojiItemService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the emoji battery customization screen.
 * Follows the established MVI pattern and architecture used throughout the app.
 * 
 * Manages:
 * - Battery style selection and customization
 * - Live preview updates
 * - Data persistence through use cases
 * - Form validation and error handling
 * - Integration with existing app architecture
 */
@HiltViewModel
class CustomizeViewModel @Inject constructor(
    private val loadCustomizationUseCase: LoadCustomizationUseCase,
    private val saveCustomizationUseCase: SaveCustomizationUseCase,
    private val emojiItemService: EmojiItemService
) : ViewModel() {
    
    companion object {
        private const val TAG = "CustomizeViewModel"
        private const val MIN_FONT_SIZE = 5
        private const val MAX_FONT_SIZE = 40
        private const val MIN_EMOJI_SCALE = 0.5f
        private const val MAX_EMOJI_SCALE = 2.0f
    }
    
    // UI State
    private val _uiState = MutableStateFlow(CustomizeState())
    val uiState: StateFlow<CustomizeState> = _uiState.asStateFlow()
    
    init {
        Log.d(TAG, "CustomizeViewModel initialized")
        Log.d(TAG, "Initial UI state: ${_uiState.value}")
        observeDataSources()
    }
    
    /**
     * Handles events from the UI.
     */
    fun handleEvent(event: CustomizeEvent) {
        Log.d(TAG, "Handling event: ${event::class.simpleName}")
        
        when (event) {
            is CustomizeEvent.LoadInitialData -> loadInitialData()
            is CustomizeEvent.InitializeWithStyle -> initializeWithStyle(event.style)
            is CustomizeEvent.SelectBatteryStyle -> selectBatteryStyle(event.style)
            is CustomizeEvent.SelectEmojiStyle -> selectEmojiStyle(event.style)
            is CustomizeEvent.ToggleGlobalEnabled -> updateGlobalEnabled(event.enabled)
            is CustomizeEvent.ToggleShowEmoji -> updateShowEmoji(event.show)
            is CustomizeEvent.ToggleShowPercentage -> updateShowPercentage(event.show)
            is CustomizeEvent.UpdatePercentageFontSize -> updatePercentageFontSize(event.size)
            is CustomizeEvent.UpdateEmojiSizeScale -> updateEmojiSizeScale(event.scale)
            is CustomizeEvent.UpdatePercentageColor -> updatePercentageColor(event.color)
            is CustomizeEvent.ShowColorPicker -> showColorPicker()
            is CustomizeEvent.HideColorPicker -> hideColorPicker()
            is CustomizeEvent.SelectColor -> selectColor(event.color, event.index)
            is CustomizeEvent.UpdatePreviewBatteryLevel -> updatePreviewBatteryLevel(event.level)
            is CustomizeEvent.RefreshPreview -> refreshPreview()
            is CustomizeEvent.ApplyCustomization -> applyCustomization()
            is CustomizeEvent.ResetToDefaults -> resetToDefaults()
            is CustomizeEvent.NavigateBack -> navigateBack()
            is CustomizeEvent.ClearNavigationState -> clearNavigationState()
            is CustomizeEvent.OnResume -> onResume()
            is CustomizeEvent.OnPause -> onPause()
            is CustomizeEvent.RetryLoad -> retryLoad()
            is CustomizeEvent.ClearError -> clearError()
            is CustomizeEvent.ValidateForm -> validateForm()
            is CustomizeEvent.ShowValidationError -> showValidationError(event.message)
        }
    }
    
    /**
     * Observes data sources and updates UI state reactively.
     */
    private fun observeDataSources() {
        viewModelScope.launch {
            try {
                // Load customization config
                loadCustomizationUseCase().collect { config ->
                    Log.d(TAG, "Customization config updated: ${config.selectedStyleId}")
                    updateState {
                        copy(
                            customizationConfig = config,
                            isGlobalEnabled = config.isGlobalEnabled,
                            showEmojiToggle = config.customConfig.showEmoji,
                            showPercentageToggle = config.customConfig.showPercentage,
                            percentageFontSize = config.customConfig.percentageFontSizeDp,
                            emojiSizeScale = config.customConfig.emojiSizeScale,
                            percentageColor = config.customConfig.percentageColor
                        )
                    }
                }
            } catch (exception: Exception) {
                Log.e(TAG, "Error setting up data observation", exception)
                updateState { copy(hasError = true, errorMessage = "Failed to initialize: ${exception.message}") }
            }
        }

        // TODO: Re-implement battery styles loading using new EmojiItemService if needed
        // For now, the customize screen will work with the single style passed to it
        Log.d(TAG, "Battery styles loading removed - using single style customization only")
    }
    
    /**
     * Loads initial data for the customization screen.
     * This includes loading style alternatives for the carousels.
     */
    private fun loadInitialData() {
        Log.d(TAG, "Loading initial data")
        updateState { copy(isLoading = true, hasError = false) }

        viewModelScope.launch {
            try {
                // Load style alternatives for the carousels
                loadStyleAlternatives()
                Log.d(TAG, "Initial data load completed successfully")
            } catch (exception: Exception) {
                Log.e(TAG, "Error loading initial data", exception)
                updateState {
                    copy(
                        isLoading = false,
                        hasError = true,
                        errorMessage = "Failed to load initial data: ${exception.message}"
                    )
                }
            }
        }
    }

    /**
     * Loads style alternatives for the battery and emoji carousels.
     * Fetches emoji items from the same category as the current style and converts them to BatteryStyle objects.
     * This implements Phase 1 requirement for populating both carousels with component-specific data.
     */
    private suspend fun loadStyleAlternatives() {
        try {
            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Starting to load style alternatives")

            val currentState = _uiState.value
            val currentStyleId = currentState.customizationConfig.selectedStyleId

            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Current style ID: $currentStyleId")

            // For now, we'll load from a default category since we don't have category info from the current style
            // In a real implementation, you might want to determine the category from the current style
            val categoryId = "animal_category" // Default category for testing

            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Loading emoji items for category: $categoryId")

            // Load emoji items from the same category
            val emojiItems = emojiItemService.getEmojiItemsByCategory(categoryId)
            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Loaded ${emojiItems.size} emoji items")

            // Convert to BatteryStyle objects
            val batteryStyles = emojiItems.map { it.toBatteryStyle() }
            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Converted to ${batteryStyles.size} battery styles")

            // Update state with the loaded alternatives
            updateState {
                copy(
                    availableBatteryStyles = batteryStyles,
                    availableEmojiStyles = batteryStyles, // Same data source for both carousels in Phase 1
                    isLoading = false,
                    isInitialLoadComplete = true
                )
            }

            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Successfully updated state with ${batteryStyles.size} styles")
            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Battery styles count: ${batteryStyles.size}")
            Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Emoji styles count: ${batteryStyles.size}")

            // Log details of loaded styles for debugging
            batteryStyles.take(3).forEachIndexed { index, style ->
                Log.d(TAG, "LOAD_STYLE_ALTERNATIVES: Style $index: name='${style.name}', id='${style.id}', batteryUrl='${style.batteryImageUrl}', emojiUrl='${style.emojiImageUrl}'")
            }

        } catch (exception: Exception) {
            Log.e(TAG, "LOAD_STYLE_ALTERNATIVES: Error loading style alternatives", exception)
            updateState {
                copy(
                    isLoading = false,
                    hasError = true,
                    errorMessage = "Failed to load style alternatives: ${exception.message}"
                )
            }
        }
    }
    
    /**
     * Initializes the screen with a specific battery style.
     * Phase 2: Properly sets up composite state for mix-and-match functionality.
     */
    private fun initializeWithStyle(style: BatteryStyle) {
        Log.d(TAG, "PHASE_2: Initializing with style: ${style.name}")
        Log.d(TAG, "PHASE_2: Setting up composite state with initial battery and emoji components")

        updateState {
            CustomizeState.forStyle(style).copy(
                // Phase 2: Initialize composite state
                selectedBatteryStyle = style, // Start with the selected style for battery
                selectedEmojiStyle = style,   // Start with the selected style for emoji
                isLoading = false,
                isInitialLoadComplete = true,
                isGlobalEnabled = _uiState.value.customizationConfig.isGlobalEnabled
            )
        }
        
        // Load alternatives for the carousels
        viewModelScope.launch {
            loadStyleAlternatives()
        }

        Log.d(TAG, "PHASE_2: Initialization complete - both components set to: ${style.name}")
    }
    

    
    /**
     * Selects a battery component style for Phase 2 mix-and-match functionality.
     * Only updates the battery component without affecting the emoji component.
     */
    private fun selectBatteryStyle(style: BatteryStyle) {
        Log.d(TAG, "PHASE_2: Selecting battery component: ${style.name}")
        Log.d(TAG, "PHASE_2: Emoji component remains unchanged")
        
        updateState {
            copy(selectedBatteryStyle = style)
        }
        refreshPreview()
        
        Log.d(TAG, "PHASE_2: Battery component updated independently")
    }

    /**
     * Selects an emoji component style for Phase 2 mix-and-match functionality.
     * Only updates the emoji component without affecting the battery component.
     */
    private fun selectEmojiStyle(style: BatteryStyle) {
        Log.d(TAG, "PHASE_2: Selecting emoji component: ${style.name}")
        Log.d(TAG, "PHASE_2: Battery component remains unchanged")
        
        updateState {
            copy(selectedEmojiStyle = style)
        }
        refreshPreview()
        
        Log.d(TAG, "PHASE_2: Emoji component updated independently")
    }
    
    /**
     * Updates the global enabled state.
     */
    private fun updateGlobalEnabled(enabled: Boolean) {
        Log.d(TAG, "Updating global enabled: $enabled")
        updateState { 
            copy(
                isGlobalEnabled = enabled,
                customizationConfig = customizationConfig.copy(isGlobalEnabled = enabled)
            ) 
        }
    }
    
    /**
     * Updates show emoji toggle.
     */
    private fun updateShowEmoji(show: Boolean) {
        Log.d(TAG, "Updating show emoji: $show")
        updateState { copy(showEmojiToggle = show) }
        refreshPreview()
    }
    
    /**
     * Updates show percentage toggle.
     */
    private fun updateShowPercentage(show: Boolean) {
        Log.d(TAG, "Updating show percentage: $show")
        updateState { copy(showPercentageToggle = show) }
        refreshPreview()
    }
    
    /**
     * Updates percentage font size with validation.
     */
    private fun updatePercentageFontSize(size: Int) {
        val validatedSize = size.coerceIn(MIN_FONT_SIZE, MAX_FONT_SIZE)
        Log.d(TAG, "Updating percentage font size: $size -> $validatedSize")
        updateState { copy(percentageFontSize = validatedSize) }
        refreshPreview()
    }
    
    /**
     * Updates emoji size scale with validation.
     */
    private fun updateEmojiSizeScale(scale: Float) {
        val validatedScale = scale.coerceIn(MIN_EMOJI_SCALE, MAX_EMOJI_SCALE)
        Log.d(TAG, "Updating emoji size scale: $scale -> $validatedScale")
        updateState { copy(emojiSizeScale = validatedScale) }
        refreshPreview()
    }
    
    /**
     * Updates percentage color.
     */
    private fun updatePercentageColor(color: Int) {
        Log.d(TAG, "Updating percentage color: $color")
        updateState { copy(percentageColor = color) }
        refreshPreview()
    }
    
    /**
     * Shows the color picker.
     */
    private fun showColorPicker() {
        Log.d(TAG, "Showing color picker")
        updateState { copy(isColorPickerVisible = true) }
    }
    
    /**
     * Hides the color picker.
     */
    private fun hideColorPicker() {
        Log.d(TAG, "Hiding color picker")
        updateState { copy(isColorPickerVisible = false) }
    }
    
    /**
     * Selects a color from the color picker.
     */
    private fun selectColor(color: Int, index: Int) {
        Log.d(TAG, "Selecting color: $color at index $index")
        updateState { 
            copy(
                percentageColor = color,
                selectedColorIndex = index,
                isColorPickerVisible = false
            ) 
        }
        refreshPreview()
    }
    
    /**
     * Updates the preview battery level.
     */
    private fun updatePreviewBatteryLevel(level: Int) {
        val validatedLevel = level.coerceIn(0, 100)
        Log.d(TAG, "Updating preview battery level: $level -> $validatedLevel")
        updateState { copy(previewBatteryLevel = validatedLevel) }
    }
    
    /**
     * Refreshes the preview configuration.
     */
    private fun refreshPreview() {
        updateState { copy(previewConfig = currentPreviewConfig) }
    }
    
    /**
     * Applies the current customization with Phase 2 composite configuration support.
     */
    private fun applyCustomization() {
        Log.d(TAG, "PHASE_2: Applying composite customization")
        
        val currentState = _uiState.value
        if (!currentState.canApplyChanges) {
            Log.w(TAG, "PHASE_2: Cannot apply changes - validation failed or missing data")
            return
        }
        
        updateState { copy(isSaving = true, hasError = false) }
        
        viewModelScope.launch {
            try {
                val batteryStyle = currentState.selectedBatteryStyle
                val emojiStyle = currentState.selectedEmojiStyle
                
                // Validate composite selection
                if (batteryStyle == null || emojiStyle == null) {
                    throw IllegalStateException("PHASE_2: Both battery and emoji components must be selected")
                }
                
                // Create composite style identifier
                val compositeStyleId = if (batteryStyle.id == emojiStyle.id) {
                    // Same style for both components - use single ID
                    batteryStyle.id
                } else {
                    // Mixed components - create composite ID
                    "${batteryStyle.id}_${emojiStyle.id}"
                }
                
                Log.d(TAG, "PHASE_2: Composite style ID: $compositeStyleId")
                Log.d(TAG, "PHASE_2: Battery component: ${batteryStyle.name} (${batteryStyle.id})")
                Log.d(TAG, "PHASE_2: Emoji component: ${emojiStyle.name} (${emojiStyle.id})")
                
                val customizationConfig = CustomizationConfig(
                    selectedStyleId = compositeStyleId,
                    customConfig = currentState.currentPreviewConfig,
                    isGlobalEnabled = currentState.isGlobalEnabled,
                    lastModifiedTimestamp = System.currentTimeMillis()
                )
                
                val result = saveCustomizationUseCase(customizationConfig)
                
                if (result.isSuccess) {
                    Log.d(TAG, "PHASE_2: Composite customization applied successfully")
                    updateState { 
                        copy(
                            isSaving = false,
                            shouldShowSuccessMessage = true,
                            shouldNavigateBack = true,
                            isCustomizationApplied = true
                        ) 
                    }
                } else {
                    throw result.exceptionOrNull() ?: Exception("Unknown error saving customization")
                }
            } catch (exception: Exception) {
                Log.e(TAG, "PHASE_2: Error applying composite customization", exception)
                updateState { 
                    copy(
                        isSaving = false,
                        hasError = true,
                        errorMessage = "Failed to apply customization: ${exception.message}"
                    ) 
                }
            }
        }
    }
    
    /**
     * Resets all customizations to defaults.
     */
    private fun resetToDefaults() {
        Log.d(TAG, "Resetting to defaults")
        
        val currentState = _uiState.value
        val selectedStyle = currentState.selectedStyle
        
        if (selectedStyle != null) {
            updateState { 
                copy(
                    showEmojiToggle = selectedStyle.defaultConfig.showEmoji,
                    showPercentageToggle = selectedStyle.defaultConfig.showPercentage,
                    percentageFontSize = selectedStyle.defaultConfig.percentageFontSizeDp,
                    emojiSizeScale = selectedStyle.defaultConfig.emojiSizeScale,
                    percentageColor = selectedStyle.defaultConfig.percentageColor,
                    selectedColorIndex = 0
                ) 
            }
            refreshPreview()
        }
    }
    
    /**
     * Navigates back to the previous screen.
     */
    private fun navigateBack() {
        Log.d(TAG, "Navigating back")
        updateState { copy(shouldNavigateBack = true) }
    }
    
    /**
     * Clears navigation state to prevent double navigation.
     */
    private fun clearNavigationState() {
        Log.d(TAG, "Clearing navigation state")
        updateState { 
            copy(
                shouldNavigateBack = false,
                shouldShowSuccessMessage = false,
                isCustomizationApplied = false
            ) 
        }
    }
    
    /**
     * Handles resume lifecycle event.
     */
    private fun onResume() {
        Log.d(TAG, "onResume")
        // Refresh data if needed
    }
    
    /**
     * Handles pause lifecycle event.
     */
    private fun onPause() {
        Log.d(TAG, "onPause")
        // Save any pending changes if needed
    }
    
    /**
     * Retries loading data after an error.
     */
    private fun retryLoad() {
        Log.d(TAG, "Retrying load")
        clearError()
        loadInitialData()
    }
    
    /**
     * Clears the current error state.
     */
    private fun clearError() {
        Log.d(TAG, "Clearing error")
        updateState { copy(hasError = false, errorMessage = "") }
    }
    
    /**
     * Validates the current form state for Phase 2 composite selection.
     */
    private fun validateForm() {
        val currentState = _uiState.value
        val errors = mutableListOf<String>()
        
        // Phase 2: Validate composite selection
        if (currentState.selectedBatteryStyle == null) {
            errors.add("Please select a battery component")
        }
        
        if (currentState.selectedEmojiStyle == null) {
            errors.add("Please select an emoji component")
        }
        
        if (currentState.percentageFontSize !in MIN_FONT_SIZE..MAX_FONT_SIZE) {
            errors.add("Font size must be between $MIN_FONT_SIZE and $MAX_FONT_SIZE")
        }
        
        if (currentState.emojiSizeScale !in MIN_EMOJI_SCALE..MAX_EMOJI_SCALE) {
            errors.add("Emoji scale must be between $MIN_EMOJI_SCALE and $MAX_EMOJI_SCALE")
        }
        
        updateState { 
            copy(
                isFormValid = errors.isEmpty(),
                validationErrors = errors
            ) 
        }
        
        Log.d(TAG, "PHASE_2: Form validation - isValid: ${errors.isEmpty()}, errors: $errors")
    }
    
    /**
     * Shows a validation error message.
     */
    private fun showValidationError(message: String) {
        Log.w(TAG, "Validation error: $message")
        updateState { 
            copy(
                hasError = true,
                errorMessage = message,
                isFormValid = false
            ) 
        }
    }
    
    /**
     * Helper function to update state safely.
     */
    private fun updateState(update: CustomizeState.() -> CustomizeState) {
        val oldState = _uiState.value
        val newState = oldState.update()
        _uiState.value = newState
        Log.d(TAG, "State updated - selectedStyle: ${newState.selectedStyle?.name}, loading: ${newState.isLoading}, error: ${newState.hasError}")
    }
}
