<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_centerInParent="false">

    <RelativeLayout
        android:background="@drawable/white_block"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="200dp"

        android:elevation="25dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <RelativeLayout
                    android:id="@+id/strelka"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignTop="@+id/textView20"
                    android:layout_alignBottom="@+id/textView20"
                    android:layout_alignParentEnd="true">
                    <Button
                        android:id="@+id/exit"
                        android:background="@drawable/grey_block_line_up"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="45sp"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="5dp"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:orientation="vertical"
                        android:background="@drawable/ic_strelka"
                        android:visibility="visible"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4sp"
                        android:layout_marginBottom="4sp"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="15sp"
                        android:layout_marginEnd="15sp"
                        android:layout_alignStart="@+id/exit"
                        android:layout_alignEnd="@+id/exit"/>
                </RelativeLayout>
                <TextView
                    android:textSize="18sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:gravity="start"
                    android:id="@+id/textView20"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/language"
                    android:singleLine="true"
                    android:layout_centerVertical="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:onClick="OnClick"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="2dp"
                    android:layout_toStartOf="@+id/strelka"
                    android:layout_alignParentStart="true"/>
            </RelativeLayout>
            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_marginTop="7dp">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/de"
                        android:background="@drawable/grey_block_line_up"
                        android:paddingTop="12.5dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/Germany_lang"
                        android:textAlignment="center"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/nl"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="12dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="12dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/Dutch_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/en"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/English_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/es"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/Spanish_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/fr"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/French_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/it"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/Italy_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/hu"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/Hungarian_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/pl"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/Poland_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/pt"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/Portuguese_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/ro"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/Romanian_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/tr"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/Turkish_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/ru"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/Russian_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/ua"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/Ukraine_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/ar"
                        android:background="@drawable/grey_block_line"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/arabic_lang"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/zh"
                        android:background="@drawable/grey_block_line_down"
                        android:paddingLeft="13dp"
                        android:paddingTop="12.5dp"
                        android:paddingRight="13dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/_hinese_traditional_lang_res_0x7f130305"
                        android:textAlignment="center"
                        android:paddingHorizontal="13dp"
                        android:paddingVertical="12.5dp"/>
                </LinearLayout>
            </ScrollView>
            <Button
                android:layout_margin="10dp"
                android:id="@+id/select_button"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="SELECT"
                android:textAllCaps="true"
                android:textColor="?attr/black"
                android:background="@drawable/grey_block"
                android:textStyle="bold"/>
            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/nativeAd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/grey" />
        </LinearLayout>



    </RelativeLayout>

</RelativeLayout>
