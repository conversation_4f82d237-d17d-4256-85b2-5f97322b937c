<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_slide_layout_5" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_slide_layout_5.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_slide_layout_5_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="166" endOffset="14"/></Target><Target id="@+id/text_percent" view="TextView"><Expressions/><location startLine="27" startOffset="12" endLine="35" endOffset="53"/></Target><Target id="@+id/circularbar" view="me.tankery.lib.circularseekbar.CircularSeekBar"><Expressions/><location startLine="36" startOffset="12" endLine="54" endOffset="37"/></Target><Target id="@+id/textView4" view="TextView"><Expressions/><location startLine="56" startOffset="8" endLine="67" endOffset="44"/></Target><Target id="@+id/p5" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="87" startOffset="12" endLine="129" endOffset="63"/></Target><Target id="@+id/p_11" view="TextView"><Expressions/><location startLine="95" startOffset="16" endLine="113" endOffset="62"/></Target><Target id="@+id/switch_info" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="114" startOffset="16" endLine="128" endOffset="55"/></Target><Target id="@+id/next_page" view="LinearLayout"><Expressions/><location startLine="132" startOffset="4" endLine="163" endOffset="18"/></Target><Target id="@+id/swipe_text" view="TextView"><Expressions/><location startLine="147" startOffset="8" endLine="156" endOffset="40"/></Target></Targets></Layout>