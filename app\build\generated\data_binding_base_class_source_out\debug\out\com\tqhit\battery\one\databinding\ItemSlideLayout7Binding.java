// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSlideLayout7Binding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView capacity;

  @NonNull
  public final Button changeCapacity;

  @NonNull
  public final TextView deviceName;

  @NonNull
  public final TextView discharging;

  @NonNull
  public final LinearLayout indentTop;

  @NonNull
  public final LinearLayout main;

  @NonNull
  public final LinearLayout nextPage;

  @NonNull
  public final TextView parameter;

  @NonNull
  public final TextView polarity;

  @NonNull
  public final CircularProgressIndicator progressbar2;

  @NonNull
  public final TextView swipeText;

  @NonNull
  public final LinearLayout text5;

  @NonNull
  public final TextView textView4;

  @NonNull
  public final TextView textViewResetCharge;

  private ItemSlideLayout7Binding(@NonNull LinearLayout rootView, @NonNull TextView capacity,
      @NonNull Button changeCapacity, @NonNull TextView deviceName, @NonNull TextView discharging,
      @NonNull LinearLayout indentTop, @NonNull LinearLayout main, @NonNull LinearLayout nextPage,
      @NonNull TextView parameter, @NonNull TextView polarity,
      @NonNull CircularProgressIndicator progressbar2, @NonNull TextView swipeText,
      @NonNull LinearLayout text5, @NonNull TextView textView4,
      @NonNull TextView textViewResetCharge) {
    this.rootView = rootView;
    this.capacity = capacity;
    this.changeCapacity = changeCapacity;
    this.deviceName = deviceName;
    this.discharging = discharging;
    this.indentTop = indentTop;
    this.main = main;
    this.nextPage = nextPage;
    this.parameter = parameter;
    this.polarity = polarity;
    this.progressbar2 = progressbar2;
    this.swipeText = swipeText;
    this.text5 = text5;
    this.textView4 = textView4;
    this.textViewResetCharge = textViewResetCharge;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSlideLayout7Binding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSlideLayout7Binding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_slide_layout_7, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSlideLayout7Binding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.capacity;
      TextView capacity = ViewBindings.findChildViewById(rootView, id);
      if (capacity == null) {
        break missingId;
      }

      id = R.id.change_capacity;
      Button changeCapacity = ViewBindings.findChildViewById(rootView, id);
      if (changeCapacity == null) {
        break missingId;
      }

      id = R.id.device_name;
      TextView deviceName = ViewBindings.findChildViewById(rootView, id);
      if (deviceName == null) {
        break missingId;
      }

      id = R.id.discharging;
      TextView discharging = ViewBindings.findChildViewById(rootView, id);
      if (discharging == null) {
        break missingId;
      }

      id = R.id.indent_top;
      LinearLayout indentTop = ViewBindings.findChildViewById(rootView, id);
      if (indentTop == null) {
        break missingId;
      }

      id = R.id.main;
      LinearLayout main = ViewBindings.findChildViewById(rootView, id);
      if (main == null) {
        break missingId;
      }

      id = R.id.next_page;
      LinearLayout nextPage = ViewBindings.findChildViewById(rootView, id);
      if (nextPage == null) {
        break missingId;
      }

      id = R.id.parameter;
      TextView parameter = ViewBindings.findChildViewById(rootView, id);
      if (parameter == null) {
        break missingId;
      }

      id = R.id.polarity;
      TextView polarity = ViewBindings.findChildViewById(rootView, id);
      if (polarity == null) {
        break missingId;
      }

      id = R.id.progressbar2;
      CircularProgressIndicator progressbar2 = ViewBindings.findChildViewById(rootView, id);
      if (progressbar2 == null) {
        break missingId;
      }

      id = R.id.swipe_text;
      TextView swipeText = ViewBindings.findChildViewById(rootView, id);
      if (swipeText == null) {
        break missingId;
      }

      id = R.id.text5;
      LinearLayout text5 = ViewBindings.findChildViewById(rootView, id);
      if (text5 == null) {
        break missingId;
      }

      id = R.id.textView4;
      TextView textView4 = ViewBindings.findChildViewById(rootView, id);
      if (textView4 == null) {
        break missingId;
      }

      id = R.id.text_view_reset_charge;
      TextView textViewResetCharge = ViewBindings.findChildViewById(rootView, id);
      if (textViewResetCharge == null) {
        break missingId;
      }

      return new ItemSlideLayout7Binding((LinearLayout) rootView, capacity, changeCapacity,
          deviceName, discharging, indentTop, main, nextPage, parameter, polarity, progressbar2,
          swipeText, text5, textView4, textViewResetCharge);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
