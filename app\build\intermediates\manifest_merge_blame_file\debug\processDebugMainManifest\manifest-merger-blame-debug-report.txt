1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
4    android:versionCode="45"
5    android:versionName="1.2.1.20250630" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
14-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:5-95
14-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:22-92
15    <uses-permission android:name="android.permission.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
15-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:5-101
15-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:22-99
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:5-76
16-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
17-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:5-88
17-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:22-86
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:5-65
18-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:22-63
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:5-78
19-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:22-75
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:5-81
20-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:22-78
21    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" /> <!-- Permissions for emoji overlay status bar functionality -->
21-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:5-16:47
21-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:22-75
22    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
22-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:18:5-75
22-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:18:22-72
23    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
23-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:19:5-81
23-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:19:22-78
24
25    <permission
25-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:21:5-23:47
26        android:name="com.tqhit.battery.one.permission.FINISH_OVERLAY"
26-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:22:9-71
27        android:protectionLevel="signature" />
27-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:23:9-44
28
29    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
29-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:5-76
29-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:22-73
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:5-68
30-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:22-65
31    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
31-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:10:5-79
31-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:10:22-76
32
33    <queries>
33-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:8:5-12:15
34        <intent>
34-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:9:9-11:18
35            <action android:name="com.attribution.REFERRAL_PROVIDER" />
35-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:10:13-72
35-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:10:21-69
36        </intent>
37        <intent>
37-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:14:9-16:18
38            <action android:name="androidx.browser.customtabs.CustomTabsService" />
38-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:15:13-84
38-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:15:21-81
39        </intent>
40        <intent>
40-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:17:9-23:18
41            <action android:name="android.intent.action.VIEW" />
41-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:18:13-65
41-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:18:21-62
42
43            <category android:name="android.intent.category.BROWSABLE" />
43-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:20:13-74
43-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:20:23-71
44
45            <data android:scheme="https" />
45-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:13-44
45-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:19-41
46        </intent>
47        <intent>
47-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:24:9-30:18
48            <action android:name="android.intent.action.VIEW" />
48-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:18:13-65
48-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:18:21-62
49
50            <category android:name="android.intent.category.BROWSABLE" />
50-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:20:13-74
50-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:20:23-71
51
52            <data android:scheme="http" />
52-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:13-44
52-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:19-41
53        </intent>
54        <intent>
54-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:31:9-35:18
55            <action android:name="android.intent.action.VIEW" />
55-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:18:13-65
55-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:18:21-62
56
57            <data android:scheme="market" />
57-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:13-44
57-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:19-41
58        </intent>
59
60        <package android:name="com.android.chrome" />
60-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:12:9-54
60-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:12:18-51
61        <package android:name="com.google.android.webview" />
61-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:13:9-62
61-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:13:18-59
62        <package android:name="com.android.webview" />
62-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:14:9-55
62-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:14:18-52
63        <package android:name="com.android.vending" /> <!-- End of browser content -->
63-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:10:9-55
63-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:10:18-52
64        <!-- For CustomTabsService -->
65        <intent>
65-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:47:9-49:18
66            <action android:name="android.support.customtabs.action.CustomTabsService" />
66-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:48:13-90
66-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:48:21-87
67        </intent> <!-- End of CustomTabsService -->
68        <!-- For MRAID capabilities -->
69        <intent>
69-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:52:9-56:18
70            <action android:name="android.intent.action.INSERT" />
70-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:53:13-67
70-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:53:21-64
71
72            <data android:mimeType="vnd.android.cursor.dir/event" />
72-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:13-44
73        </intent>
74        <intent>
74-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:57:9-61:18
75            <action android:name="android.intent.action.VIEW" />
75-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:18:13-65
75-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:18:21-62
76
77            <data android:scheme="sms" />
77-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:13-44
77-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:19-41
78        </intent>
79        <intent>
79-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:62:9-66:18
80            <action android:name="android.intent.action.DIAL" />
80-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:63:13-65
80-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:63:21-62
81
82            <data android:path="tel:" />
82-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:13-44
83        </intent>
84        <intent>
84-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:8:9-10:18
85            <action android:name="com.applovin.am.intent.action.APPHUB_SERVICE" />
85-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:9:13-83
85-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:9:21-80
86        </intent>
87        <intent>
87-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:18:9-22:18
88            <action android:name="android.intent.action.ACTION_VIEW" />
88-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:19:13-72
88-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:19:21-69
89
90            <data android:scheme="https" />
90-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:13-44
90-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:19-41
91        </intent>
92
93        <package android:name="com.facebook.katana" />
93-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:12:9-55
93-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:12:18-52
94
95        <intent>
95-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:15:9-17:18
96            <action android:name="com.digitalturbine.ignite.cl.IgniteRemoteService" />
96-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:16:13-87
96-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:16:21-84
97        </intent>
98        <intent>
98-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:12:9-14:18
99            <action android:name="android.intent.action.MAIN" />
99-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:47:17-69
99-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:47:25-66
100        </intent>
101        <intent>
101-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:15:9-17:18
102            <action android:name="android.intent.action.VIEW" />
102-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:18:13-65
102-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:18:21-62
103        </intent>
104    </queries>
105
106    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
106-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:17:5-83
106-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:17:22-80
107    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
107-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:18:5-88
107-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:18:22-85
108    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
108-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:27:5-82
108-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:27:22-79
109    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
109-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
109-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
110    <uses-permission android:name="com.applovin.array.apphub.permission.BIND_APPHUB_SERVICE" />
110-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:20:5-96
110-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:20:22-93
111
112    <permission
112-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
113        android:name="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
113-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
114        android:protectionLevel="signature" />
114-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
115
116    <uses-permission android:name="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
116-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
116-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
117
118    <application
118-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:25:5-136:19
119        android:name="com.tqhit.battery.one.BatteryApplication"
119-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:26:9-64
120        android:allowBackup="true"
120-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:27:9-35
121        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
121-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
122        android:dataExtractionRules="@xml/data_extraction_rules"
122-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:28:9-65
123        android:debuggable="true"
124        android:extractNativeLibs="false"
125        android:fullBackupContent="@xml/backup_rules"
125-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:29:9-54
126        android:hardwareAccelerated="true"
126-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:15:18-52
127        android:icon="@mipmap/ic_launcher"
127-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:30:9-43
128        android:label="@string/app_name"
128-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:31:9-41
129        android:roundIcon="@mipmap/ic_launcher_round"
129-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:32:9-54
130        android:screenOrientation="portrait"
130-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:33:9-45
131        android:supportsRtl="true"
131-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:34:9-35
132        android:theme="@style/Theme.BatteryOne" >
132-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:35:9-48
133        <!-- Sample AdMob app ID: ca-app-pub-3940256099942544~3347511713 -->
134        <meta-data
134-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:38:9-40:70
135            android:name="com.google.android.gms.ads.APPLICATION_ID"
135-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:39:13-69
136            android:value="ca-app-pub-9844172086883515~3386117176" />
136-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:40:13-67
137
138        <activity
138-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:42:9-50:20
139            android:name="com.tqhit.battery.one.activity.splash.SplashActivity"
139-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:43:13-80
140            android:exported="true"
140-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:13-36
141            android:theme="@style/Theme.BatteryOne.Splash" >
141-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:45:13-59
142            <intent-filter>
142-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:46:13-49:29
143                <action android:name="android.intent.action.MAIN" />
143-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:47:17-69
143-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:47:25-66
144
145                <category android:name="android.intent.category.LAUNCHER" />
145-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:48:17-77
145-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:48:27-74
146            </intent-filter>
147        </activity>
148        <activity
148-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:52:9-56:54
149            android:name="com.tqhit.battery.one.activity.starting.StartingActivity"
149-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:53:13-84
150            android:exported="false"
150-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:54:13-37
151            android:screenOrientation="portrait"
151-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:55:13-49
152            android:theme="@style/Theme.BatteryOne" />
152-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:56:13-52
153        <activity
153-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:58:9-62:54
154            android:name="com.tqhit.battery.one.activity.onboarding.LanguageSelectionActivity"
154-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:59:13-95
155            android:exported="false"
155-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:60:13-37
156            android:screenOrientation="portrait"
156-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:61:13-49
157            android:theme="@style/Theme.BatteryOne" />
157-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:62:13-52
158        <activity
158-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:64:9-68:51
159            android:name="com.tqhit.battery.one.activity.main.MainActivity"
159-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:65:13-76
160            android:exported="true"
160-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:66:13-36
161            android:launchMode="singleTask"
161-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:67:13-44
162            android:screenOrientation="portrait" />
162-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:68:13-49
163        <activity
163-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:70:9-76:92
164            android:name="com.tqhit.battery.one.features.emoji.presentation.customize.EmojiCustomizeActivity"
164-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:71:13-110
165            android:exported="true"
165-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:72:13-36
166            android:launchMode="singleTask"
166-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:73:13-44
167            android:parentActivityName="com.tqhit.battery.one.activity.main.MainActivity"
167-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:76:13-90
168            android:screenOrientation="portrait"
168-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:74:13-49
169            android:theme="@style/Theme.BatteryOne" />
169-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:75:13-52
170        <activity
170-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:78:9-82:51
171            android:name="com.tqhit.battery.one.activity.animation.AnimationActivity"
171-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:79:13-86
172            android:configChanges="orientation|screenSize"
172-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:80:13-59
173            android:launchMode="singleTask"
173-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:81:13-44
174            android:screenOrientation="portrait" />
174-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:82:13-49
175        <activity
175-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:84:9-90:51
176            android:name="com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity"
176-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:85:13-90
177            android:configChanges="orientation|screenSize"
177-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:88:13-59
178            android:exported="false"
178-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:86:13-37
179            android:launchMode="singleTask"
179-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:13-44
180            android:screenOrientation="portrait"
180-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:13-49
181            android:theme="@style/Theme.AppCompat.NoActionBar" />
181-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:87:13-63
182        <activity
182-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:9-98:51
183            android:name="com.tqhit.battery.one.activity.password.EnterPasswordActivity"
183-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:93:13-89
184            android:configChanges="orientation|screenSize"
184-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:96:13-59
185            android:exported="false"
185-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:94:13-37
186            android:launchMode="singleTask"
186-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:97:13-44
187            android:screenOrientation="portrait"
187-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:98:13-49
188            android:theme="@style/Theme.AppCompat.NoActionBar" /> <!-- DebugActivity will be conditionally included via build variant manifests -->
188-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:95:13-63
189        <service
189-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:101:9-105:59
190            android:name="com.tqhit.battery.one.service.ChargingOverlayService"
190-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:102:13-80
191            android:enabled="true"
191-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:103:13-35
192            android:exported="false"
192-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:104:13-37
193            android:foregroundServiceType="specialUse" /> <!-- Legacy services removed - replaced by CoreBatteryStatsService and UnifiedBatteryNotificationService -->
193-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:105:13-55
194        <!-- NewChargeMonitorService, DischargeTimerService, and BatteryStatusService were in legacy directory -->
195        <service
195-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:108:9-112:59
196            android:name="com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService"
196-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:109:13-112
197            android:enabled="true"
197-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:110:13-35
198            android:exported="false"
198-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:111:13-37
199            android:foregroundServiceType="specialUse" />
199-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:112:13-55
200        <service
200-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:113:9-117:59
201            android:name="com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService"
201-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:114:13-108
202            android:enabled="true"
202-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:115:13-35
203            android:exported="false"
203-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:116:13-37
204            android:foregroundServiceType="specialUse" />
204-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:117:13-55
205        <service
205-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:118:9-122:59
206            android:name="com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService"
206-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:119:13-112
207            android:enabled="true"
207-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:120:13-35
208            android:exported="false"
208-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:121:13-37
209            android:foregroundServiceType="specialUse" /> <!-- Emoji Battery Accessibility Service -->
209-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:122:13-55
210        <service
210-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:125:9-135:19
211            android:name="com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService"
211-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:126:13-118
212            android:exported="true"
212-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:128:13-36
213            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
213-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:127:13-79
214            <intent-filter>
214-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:129:13-131:29
215                <action android:name="android.accessibilityservice.AccessibilityService" />
215-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:130:17-92
215-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:130:25-89
216            </intent-filter>
217
218            <meta-data
218-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:132:13-134:86
219                android:name="android.accessibilityservice"
219-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:133:17-60
220                android:resource="@xml/emoji_battery_accessibility_service_config" />
220-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:134:17-83
221        </service> <!-- HYPR Activities -->
222        <activity
222-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:13:9-19:59
223            android:name="com.hyprmx.android.sdk.activity.HyprMXOfferViewerActivity"
223-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:14:13-85
224            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
224-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:15:13-113
225            android:hardwareAccelerated="true"
225-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:16:13-47
226            android:label="HyprMX SDK"
226-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:17:13-39
227            android:launchMode="singleTop"
227-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:18:13-43
228            android:theme="@style/hyprmx_ActivityTheme" />
228-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:19:13-56
229        <activity
229-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:20:9-23:63
230            android:name="com.hyprmx.android.sdk.activity.HyprMXRequiredInformationActivity"
230-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:21:13-93
231            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
231-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:22:13-113
232            android:theme="@style/hyprmx_RequiredInfoTheme" />
232-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:23:13-60
233        <activity
233-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:24:9-27:59
234            android:name="com.hyprmx.android.sdk.activity.HyprMXNoOffersActivity"
234-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:25:13-82
235            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
235-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:26:13-113
236            android:theme="@style/hyprmx_ActivityTheme" />
236-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:27:13-56
237        <activity
237-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:28:9-32:59
238            android:name="com.hyprmx.android.sdk.overlay.HyprMXBrowserActivity"
238-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:29:13-80
239            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
239-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:30:13-113
240            android:hardwareAccelerated="true"
240-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:31:13-47
241            android:theme="@style/hyprmx_ActivityTheme" /> <!-- XENOSS core -->
241-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:32:13-56
242        <!-- android:initOrder="-**********" Try to initialize after all the rest of content providers -->
243        <provider
243-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:38:9-47:20
244            android:name="androidx.startup.InitializationProvider"
244-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:39:13-67
245            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.androidx-startup"
245-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:40:13-68
246            android:exported="false"
246-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:41:13-37
247            android:initOrder="-**********" >
247-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:42:13-44
248            <meta-data
248-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:44:13-46:52
249                android:name="com.moloco.sdk.internal.android_context.StartupComponentInitialization"
249-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:45:17-102
250                android:value="androidx.startup" />
250-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:46:17-49
251            <meta-data
251-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:56:13-58:52
252                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
252-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:57:17-78
253                android:value="androidx.startup" />
253-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:58:17-49
254            <meta-data
254-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:59:13-61:52
255                android:name="com.unity3d.services.core.configuration.AdsSdkInitializer"
255-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:60:17-89
256                android:value="androidx.startup" />
256-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:61:17-49
257            <meta-data
257-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
258                android:name="androidx.emoji2.text.EmojiCompatInitializer"
258-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
259                android:value="androidx.startup" />
259-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
260            <meta-data
260-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
261                android:name="androidx.work.WorkManagerInitializer"
261-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
262                android:value="androidx.startup" />
262-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
263            <meta-data
263-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
264                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
264-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
265                android:value="androidx.startup" />
265-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
266        </provider> <!-- XENOSS renderer -->
267        <activity
267-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:51:9-56:20
268            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.templates.renderer.fullscreen.FullscreenWebviewActivity"
268-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:52:13-143
269            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
269-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:53:13-87
270            android:exported="false"
270-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:54:13-37
271            android:theme="@style/FullscreenAdActivity" >
271-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:55:13-56
272        </activity>
273        <activity
273-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:57:9-62:59
274            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.vast.VastActivity"
274-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:58:13-105
275            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
275-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:59:13-87
276            android:exported="false"
276-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:60:13-37
277            android:screenOrientation="fullSensor"
277-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:61:13-51
278            android:theme="@style/FullscreenAdActivity" />
278-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:62:13-56
279        <activity
279-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:63:9-68:59
280            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.staticrenderer.StaticAdActivity"
280-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:64:13-119
281            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
281-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:65:13-87
282            android:exported="false"
282-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:66:13-37
283            android:screenOrientation="fullSensor"
283-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:67:13-51
284            android:theme="@style/FullscreenAdActivity" />
284-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:68:13-56
285        <activity
285-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:69:9-74:59
286            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.mraid.MraidActivity"
286-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:70:13-107
287            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
287-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:71:13-87
288            android:exported="false"
288-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:72:13-37
289            android:screenOrientation="fullSensor"
289-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:73:13-51
290            android:theme="@style/FullscreenAdActivity" />
290-->[com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:74:13-56
291        <activity
291-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:10:9-12:49
292            android:name="cat.ereza.customactivityoncrash.activity.DefaultErrorActivity"
292-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:11:13-89
293            android:process=":error_activity" />
293-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:12:13-46
294
295        <provider
295-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:14:9-18:39
296            android:name="cat.ereza.customactivityoncrash.provider.CaocInitProvider"
296-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:15:13-85
297            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.customactivityoncrashinitprovider"
297-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:16:13-85
298            android:exported="false"
298-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:17:13-37
299            android:initOrder="101" />
299-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:18:13-36
300
301        <activity
301-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:14:9-19:46
302            android:name="com.yandex.mobile.ads.common.AdActivity"
302-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:15:13-67
303            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
303-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:16:13-122
304            android:enableOnBackInvokedCallback="false"
304-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:17:13-56
305            android:theme="@style/MonetizationAdsInternal.AdActivity" />
305-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:18:13-70
306
307        <provider
307-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:21:9-24:40
308            android:name="com.yandex.mobile.ads.core.initializer.MobileAdsInitializeProvider"
308-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:22:13-94
309            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.MobileAdsInitializeProvider"
309-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:23:13-79
310            android:exported="false" />
310-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:24:13-37
311
312        <activity
312-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:26:9-31:46
313            android:name="com.yandex.mobile.ads.features.debugpanel.ui.IntegrationInspectorActivity"
313-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:27:13-101
314            android:enableOnBackInvokedCallback="false"
314-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:28:13-56
315            android:exported="false"
315-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:29:13-37
316            android:theme="@style/DebugPanelTheme" />
316-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:30:13-51
317
318        <provider
318-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:33:9-41:20
319            android:name="com.yandex.mobile.ads.features.debugpanel.data.local.DebugPanelFileProvider"
319-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:34:13-103
320            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.monetization.ads.inspector.fileprovider"
320-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:35:13-91
321            android:exported="false"
321-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:36:13-37
322            android:grantUriPermissions="true" >
322-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:37:13-47
323            <meta-data
323-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:38:13-40:66
324                android:name="android.support.FILE_PROVIDER_PATHS"
324-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:39:17-67
325                android:resource="@xml/debug_panel_file_paths" />
325-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:40:17-63
326        </provider>
327
328        <activity
328-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:21:9-26:74
329            android:name="com.unity3d.services.ads.adunit.AdUnitActivity"
329-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:22:13-74
330            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
330-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:23:13-170
331            android:exported="false"
331-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:24:13-37
332            android:hardwareAccelerated="true"
332-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:25:13-47
333            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
333-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:26:13-71
334        <activity
334-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:27:9-32:86
335            android:name="com.unity3d.services.ads.adunit.AdUnitTransparentActivity"
335-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:28:13-85
336            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
336-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:29:13-170
337            android:exported="false"
337-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:30:13-37
338            android:hardwareAccelerated="true"
338-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:31:13-47
339            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
339-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:32:13-83
340        <activity
340-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:33:9-38:86
341            android:name="com.unity3d.services.ads.adunit.AdUnitTransparentSoftwareActivity"
341-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:34:13-93
342            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
342-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:35:13-170
343            android:exported="false"
343-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:36:13-37
344            android:hardwareAccelerated="false"
344-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:37:13-48
345            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
345-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:38:13-83
346        <activity
346-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:39:9-44:74
347            android:name="com.unity3d.services.ads.adunit.AdUnitSoftwareActivity"
347-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:40:13-82
348            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
348-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:41:13-170
349            android:exported="false"
349-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:42:13-37
350            android:hardwareAccelerated="false"
350-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:43:13-48
351            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
351-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:44:13-71
352        <activity
352-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:45:9-50:74
353            android:name="com.unity3d.ads.adplayer.FullScreenWebViewDisplay"
353-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:46:13-77
354            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
354-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:47:13-170
355            android:exported="false"
355-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:48:13-37
356            android:hardwareAccelerated="true"
356-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:49:13-47
357            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
357-->[com.unity3d.ads:unity-ads:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3051d70704481ccb925803629e0b91a\transformed\jetified-unity-ads-4.15.1\AndroidManifest.xml:50:13-71
358        <activity
358-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:39:9-44:46
359            android:name="com.inmobi.ads.rendering.InMobiAdActivity"
359-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:40:13-69
360            android:configChanges="keyboardHidden|orientation|keyboard|smallestScreenSize|screenSize|screenLayout|locale|fontScale|uiMode"
360-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:41:13-139
361            android:hardwareAccelerated="true"
361-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:42:13-47
362            android:theme="@android:style/Theme.NoTitleBar" />
362-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:43:13-60
363
364        <meta-data
364-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:26:9-28:39
365            android:name="com.bytedance.sdk.pangle.version"
365-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:27:13-60
366            android:value="7.2.0.6" /> <!-- 下面的activity和service必须注册 -->
366-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:28:13-36
367        <activity
367-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:30:9-33:45
368            android:name="com.bytedance.sdk.openadsdk.activity.TTCeilingLandingPageActivity"
368-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:31:13-93
369            android:configChanges="keyboardHidden|orientation|screenSize"
369-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:32:13-74
370            android:launchMode="standard" />
370-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:33:13-42
371        <activity
371-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:34:9-38:54
372            android:name="com.bytedance.sdk.openadsdk.activity.TTLandingPageActivity"
372-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:35:13-86
373            android:configChanges="keyboardHidden|orientation|screenSize"
373-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:36:13-74
374            android:launchMode="standard"
374-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:37:13-42
375            android:theme="@style/tt_landing_page" />
375-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:38:13-51
376        <activity
376-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:39:9-43:54
377            android:name="com.bytedance.sdk.openadsdk.activity.TTPlayableLandingPageActivity"
377-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:40:13-94
378            android:configChanges="keyboardHidden|orientation|screenSize"
378-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:41:13-74
379            android:launchMode="standard"
379-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:42:13-42
380            android:theme="@style/tt_landing_page" />
380-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:43:13-51
381        <activity
381-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:44:9-48:54
382            android:name="com.bytedance.sdk.openadsdk.activity.TTVideoLandingPageLink2Activity"
382-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:45:13-96
383            android:configChanges="keyboardHidden|orientation|screenSize"
383-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:46:13-74
384            android:launchMode="standard"
384-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:47:13-42
385            android:theme="@style/tt_landing_page" />
385-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:48:13-51
386        <activity
386-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:49:9-52:75
387            android:name="com.bytedance.sdk.openadsdk.activity.TTDelegateActivity"
387-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:50:13-83
388            android:launchMode="standard"
388-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:51:13-42
389            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
389-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:52:13-72
390        <activity
390-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:53:9-57:62
391            android:name="com.bytedance.sdk.openadsdk.activity.TTWebsiteActivity"
391-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:54:13-82
392            android:launchMode="standard"
392-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:55:13-42
393            android:screenOrientation="portrait"
393-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:56:13-49
394            android:theme="@style/tt_privacy_landing_page" />
394-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:57:13-59
395
396        <service android:name="com.bytedance.sdk.openadsdk.multipro.aidl.BinderPoolService" />
396-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:59:9-95
396-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:59:18-92
397
398        <activity
398-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:61:9-65:66
399            android:name="com.bytedance.sdk.openadsdk.activity.TTAppOpenAdActivity"
399-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:62:13-84
400            android:configChanges="keyboardHidden|orientation|screenSize"
400-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:63:13-74
401            android:launchMode="standard"
401-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:64:13-42
402            android:theme="@style/tt_app_open_ad_no_animation" />
402-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:65:13-63
403        <activity
403-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:66:9-70:57
404            android:name="com.bytedance.sdk.openadsdk.activity.TTRewardVideoActivity"
404-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:67:13-86
405            android:configChanges="keyboardHidden|orientation|screenSize"
405-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:68:13-74
406            android:launchMode="standard"
406-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:69:13-42
407            android:theme="@style/tt_full_screen_new" />
407-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:70:13-54
408        <activity
408-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:71:9-75:57
409            android:name="com.bytedance.sdk.openadsdk.activity.TTRewardExpressVideoActivity"
409-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:72:13-93
410            android:configChanges="keyboardHidden|orientation|screenSize"
410-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:73:13-74
411            android:launchMode="standard"
411-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:74:13-42
412            android:theme="@style/tt_full_screen_new" />
412-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:75:13-54
413        <activity
413-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:76:9-80:57
414            android:name="com.bytedance.sdk.openadsdk.activity.TTFullScreenVideoActivity"
414-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:77:13-90
415            android:configChanges="keyboardHidden|orientation|screenSize"
415-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:78:13-74
416            android:launchMode="standard"
416-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:79:13-42
417            android:theme="@style/tt_full_screen_new" />
417-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:80:13-54
418        <activity
418-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:81:9-85:57
419            android:name="com.bytedance.sdk.openadsdk.activity.TTFullScreenExpressVideoActivity"
419-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:82:13-97
420            android:configChanges="keyboardHidden|orientation|screenSize"
420-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:83:13-74
421            android:launchMode="standard"
421-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:84:13-42
422            android:theme="@style/tt_full_screen_new" />
422-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:85:13-54
423        <activity
423-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:86:9-90:65
424            android:name="com.bytedance.sdk.openadsdk.activity.TTInterstitialActivity"
424-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:87:13-87
425            android:configChanges="keyboardHidden|orientation|screenSize"
425-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:88:13-74
426            android:launchMode="standard"
426-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:89:13-42
427            android:theme="@style/tt_full_screen_interaction" />
427-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:90:13-62
428        <activity
428-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:91:9-95:65
429            android:name="com.bytedance.sdk.openadsdk.activity.TTInterstitialExpressActivity"
429-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:92:13-94
430            android:configChanges="keyboardHidden|orientation|screenSize"
430-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:93:13-74
431            android:launchMode="standard"
431-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:94:13-42
432            android:theme="@style/tt_full_screen_interaction" />
432-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:95:13-62
433        <activity
433-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:96:9-100:57
434            android:name="com.bytedance.sdk.openadsdk.activity.TTAdActivity"
434-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:97:13-77
435            android:configChanges="keyboardHidden|orientation|screenSize"
435-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:98:13-74
436            android:launchMode="standard"
436-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:99:13-42
437            android:theme="@style/tt_full_screen_new" />
437-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:100:13-54
438
439        <provider
439-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:18:9-22:39
440            android:name="io.bidmachine.BidMachineInitProvider"
440-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:19:13-64
441            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.bidmachineinitprovider"
441-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:20:13-74
442            android:exported="false"
442-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:21:13-37
443            android:initOrder="100" />
443-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:22:13-36
444
445        <activity
445-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:24:9-26:80
446            android:name="io.bidmachine.nativead.view.VideoPlayerActivity"
446-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:25:13-75
447            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" />
447-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:26:13-77
448        <activity
448-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:29:9-33:74
449            android:name="io.bidmachine.rendering.ad.fullscreen.FullScreenActivity"
449-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:30:13-84
450            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
450-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:31:13-122
451            android:hardwareAccelerated="true"
451-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:32:13-47
452            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
452-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:33:13-71
453        <activity
453-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:34:9-38:74
454            android:name="io.bidmachine.iab.mraid.MraidActivity"
454-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:35:13-65
455            android:configChanges="keyboardHidden|orientation|screenSize"
455-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:36:13-74
456            android:hardwareAccelerated="true"
456-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:37:13-47
457            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
457-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:38:13-71
458        <activity
458-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:39:9-43:74
459            android:name="io.bidmachine.iab.vast.activity.VastActivity"
459-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:40:13-72
460            android:configChanges="keyboardHidden|orientation|screenSize"
460-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:41:13-74
461            android:hardwareAccelerated="true"
461-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:42:13-47
462            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
462-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:43:13-71
463        <activity
463-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:73:9-78:43
464            android:name="com.google.android.gms.ads.AdActivity"
464-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:74:13-65
465            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
465-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:75:13-122
466            android:exported="false"
466-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:76:13-37
467            android:theme="@android:style/Theme.Translucent" />
467-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:77:13-61
468
469        <provider
469-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:80:9-85:43
470            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
470-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:81:13-76
471            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.mobileadsinitprovider"
471-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:82:13-73
472            android:exported="false"
472-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:83:13-37
473            android:initOrder="100" />
473-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:84:13-36
474
475        <service
475-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:87:9-91:43
476            android:name="com.google.android.gms.ads.AdService"
476-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:88:13-64
477            android:enabled="true"
477-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:89:13-35
478            android:exported="false" />
478-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:90:13-37
479
480        <activity
480-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:93:9-97:43
481            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
481-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:94:13-82
482            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
482-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:95:13-122
483            android:exported="false" />
483-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:96:13-37
484        <activity
484-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:98:9-105:43
485            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
485-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:99:13-82
486            android:excludeFromRecents="true"
486-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:100:13-46
487            android:exported="false"
487-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:101:13-37
488            android:launchMode="singleTask"
488-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:102:13-44
489            android:taskAffinity=""
489-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:103:13-36
490            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
490-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:104:13-72
491
492        <meta-data
492-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:107:9-109:36
493            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
493-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:108:13-79
494            android:value="true" />
494-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:109:13-33
495        <meta-data
495-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:110:9-112:36
496            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
496-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:111:13-83
497            android:value="true" />
497-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:112:13-33
498
499        <receiver
499-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
500            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
500-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
501            android:enabled="true"
501-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
502            android:exported="false" >
502-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
503        </receiver>
504
505        <service
505-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
506            android:name="com.google.android.gms.measurement.AppMeasurementService"
506-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
507            android:enabled="true"
507-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
508            android:exported="false" />
508-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
509        <service
509-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
510            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
510-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
511            android:enabled="true"
511-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
512            android:exported="false"
512-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
513            android:permission="android.permission.BIND_JOB_SERVICE" />
513-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
514        <service
514-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
515            android:name="com.google.firebase.components.ComponentDiscoveryService"
515-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
516            android:directBootAware="true"
516-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
517            android:exported="false" >
517-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
518            <meta-data
518-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
519                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
519-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
520                android:value="com.google.firebase.components.ComponentRegistrar" />
520-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
521            <meta-data
521-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:15:13-17:85
522                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
522-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:16:17-126
523                android:value="com.google.firebase.components.ComponentRegistrar" />
523-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:17:17-82
524            <meta-data
524-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:18:13-20:85
525                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
525-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:19:17-115
526                android:value="com.google.firebase.components.ComponentRegistrar" />
526-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:20:17-82
527            <meta-data
527-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:29:13-31:85
528                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
528-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:30:17-128
529                android:value="com.google.firebase.components.ComponentRegistrar" />
529-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:31:17-82
530            <meta-data
530-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:32:13-34:85
531                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
531-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:33:17-117
532                android:value="com.google.firebase.components.ComponentRegistrar" />
532-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:34:17-82
533            <meta-data
533-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:29:13-31:85
534                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
534-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:30:17-117
535                android:value="com.google.firebase.components.ComponentRegistrar" />
535-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:31:17-82
536            <meta-data
536-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
537                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
537-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
538                android:value="com.google.firebase.components.ComponentRegistrar" />
538-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
539            <meta-data
539-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
540                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
540-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
541                android:value="com.google.firebase.components.ComponentRegistrar" />
541-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
542            <meta-data
542-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
543                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
543-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
544                android:value="com.google.firebase.components.ComponentRegistrar" />
544-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
545            <meta-data
545-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
546                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
546-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
547                android:value="com.google.firebase.components.ComponentRegistrar" />
547-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
548            <meta-data
548-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
549                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
549-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
550                android:value="com.google.firebase.components.ComponentRegistrar" />
550-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
551            <meta-data
551-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
552                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
552-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
553                android:value="com.google.firebase.components.ComponentRegistrar" />
553-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
554        </service>
555
556        <provider
556-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:26:9-30:39
557            android:name="com.applovin.sdk.AppLovinInitProvider"
557-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:27:13-65
558            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.applovininitprovider"
558-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:28:13-72
559            android:exported="false"
559-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:29:13-37
560            android:initOrder="101" /> <!-- Init order is 101 so we're before Firebase/Google which uses 100 -->
560-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:30:13-36
561        <activity
561-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:32:9-39:74
562            android:name="com.applovin.adview.AppLovinFullscreenActivity"
562-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:33:13-74
563            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
563-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:34:13-139
564            android:exported="false"
564-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:35:13-37
565            android:hardwareAccelerated="true"
565-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:36:13-47
566            android:launchMode="singleTop"
566-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:37:13-43
567            android:screenOrientation="behind"
567-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:38:13-47
568            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
568-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:39:13-71
569        <activity
569-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:40:9-42:142
570            android:name="com.applovin.sdk.AppLovinWebViewActivity"
570-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:41:13-68
571            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" /> <!-- Mediation Debugger Activities -->
571-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:42:13-139
572        <activity
572-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:45:9-48:87
573            android:name="com.applovin.mediation.MaxDebuggerActivity"
573-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:46:13-70
574            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
574-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:47:13-139
575            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
575-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:48:13-84
576        <activity
576-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:49:9-52:87
577            android:name="com.applovin.mediation.MaxDebuggerDetailActivity"
577-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:50:13-76
578            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
578-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:51:13-139
579            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
579-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:52:13-84
580        <activity
580-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:53:9-56:87
581            android:name="com.applovin.mediation.MaxDebuggerMultiAdActivity"
581-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:54:13-77
582            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
582-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:55:13-139
583            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
583-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:56:13-84
584        <activity
584-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:57:9-60:87
585            android:name="com.applovin.mediation.MaxDebuggerAdUnitsListActivity"
585-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:58:13-81
586            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
586-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:59:13-139
587            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
587-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:60:13-84
588        <activity
588-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:61:9-64:87
589            android:name="com.applovin.mediation.MaxDebuggerAdUnitWaterfallsListActivity"
589-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:62:13-90
590            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
590-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:63:13-139
591            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
591-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:64:13-84
592        <activity
592-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:65:9-68:87
593            android:name="com.applovin.mediation.MaxDebuggerAdUnitDetailActivity"
593-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:66:13-82
594            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
594-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:67:13-139
595            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
595-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:68:13-84
596        <activity
596-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:69:9-72:87
597            android:name="com.applovin.mediation.MaxDebuggerCmpNetworksListActivity"
597-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:70:13-85
598            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
598-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:71:13-139
599            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
599-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:72:13-84
600        <activity
600-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:73:9-76:87
601            android:name="com.applovin.mediation.MaxDebuggerTcfConsentStatusesListActivity"
601-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:74:13-92
602            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
602-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:75:13-139
603            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
603-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:76:13-84
604        <activity
604-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:77:9-80:87
605            android:name="com.applovin.mediation.MaxDebuggerTcfInfoListActivity"
605-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:78:13-81
606            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
606-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:79:13-139
607            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
607-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:80:13-84
608        <activity
608-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:81:9-84:87
609            android:name="com.applovin.mediation.MaxDebuggerTcfStringActivity"
609-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:82:13-79
610            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
610-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:83:13-139
611            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
611-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:84:13-84
612        <activity
612-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:85:9-88:87
613            android:name="com.applovin.mediation.MaxDebuggerTestLiveNetworkActivity"
613-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:86:13-85
614            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
614-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:87:13-139
615            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
615-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:88:13-84
616        <activity
616-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:89:9-92:87
617            android:name="com.applovin.mediation.MaxDebuggerTestModeNetworkActivity"
617-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:90:13-85
618            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
618-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:91:13-139
619            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
619-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:92:13-84
620        <activity
620-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:93:9-96:87
621            android:name="com.applovin.mediation.MaxDebuggerUnifiedFlowActivity"
621-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:94:13-81
622            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
622-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:95:13-139
623            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
623-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:96:13-84
624        <activity
624-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:97:9-100:87
625            android:name="com.applovin.mediation.MaxDebuggerWaterfallSegmentsActivity"
625-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:98:13-87
626            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
626-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:99:13-139
627            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
627-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:100:13-84
628        <activity
628-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:101:9-104:91
629            android:name="com.applovin.creative.MaxCreativeDebuggerActivity"
629-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:102:13-77
630            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
630-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:103:13-139
631            android:theme="@style/com.applovin.creative.CreativeDebuggerActivity.Theme" />
631-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:104:13-88
632        <activity
632-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:105:9-108:91
633            android:name="com.applovin.creative.MaxCreativeDebuggerDisplayedAdActivity"
633-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:106:13-88
634            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
634-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:107:13-139
635            android:theme="@style/com.applovin.creative.CreativeDebuggerActivity.Theme" /> <!-- Services -->
635-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:108:13-88
636        <service
636-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:111:9-114:44
637            android:name="com.applovin.impl.adview.activity.FullscreenAdService"
637-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:112:13-81
638            android:exported="false"
638-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:113:13-37
639            android:stopWithTask="false" />
639-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:114:13-41
640
641        <activity
641-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:26:9-33:80
642            android:name="com.chartboost.sdk.view.CBImpressionActivity"
642-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:27:13-72
643            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
643-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:28:13-106
644            android:enableOnBackInvokedCallback="true"
644-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:29:13-55
645            android:excludeFromRecents="true"
645-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:30:13-46
646            android:exported="false"
646-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:31:13-37
647            android:hardwareAccelerated="true"
647-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:32:13-47
648            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" />
648-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:33:13-77
649        <activity
649-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:34:9-40:86
650            android:name="com.chartboost.sdk.internal.clickthrough.EmbeddedBrowserActivity"
650-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:35:13-92
651            android:configChanges="keyboardHidden|orientation|screenSize"
651-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:36:13-74
652            android:excludeFromRecents="true"
652-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:37:13-46
653            android:exported="false"
653-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:38:13-37
654            android:hardwareAccelerated="true"
654-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:39:13-47
655            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" /> <!-- ExoPlayer DownloadService -->
655-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:40:13-83
656        <service
656-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:43:9-53:19
657            android:name="com.chartboost.sdk.internal.video.repository.exoplayer.VideoRepositoryDownloadService"
657-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:44:13-113
658            android:exported="false" >
658-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:45:13-37
659
660            <!-- This is needed for Scheduler -->
661            <intent-filter>
661-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:48:13-52:29
662                <action android:name="com.google.android.exoplayer.downloadService.action.RESTART" />
662-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:49:17-102
662-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:49:25-99
663
664                <category android:name="android.intent.category.DEFAULT" />
664-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:51:17-76
664-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:51:27-73
665            </intent-filter>
666        </service> <!-- AppMetrica Analytics: common service -->
667        <service
667-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:15:9-26:19
668            android:name="io.appmetrica.analytics.internal.AppMetricaService"
668-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:16:13-78
669            android:enabled="true"
669-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:17:13-35
670            android:exported="false" >
670-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:18:13-37
671            <intent-filter>
671-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:19:13-25:29
672                <category android:name="android.intent.category.DEFAULT" />
672-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:51:17-76
672-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:51:27-73
673
674                <action android:name="io.appmetrica.analytics.IAppMetricaService" />
674-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:22:17-85
674-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:22:25-82
675
676                <data android:scheme="appmetrica" />
676-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:13-44
676-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:22:19-41
677            </intent-filter>
678        </service> <!-- To track preinstallations -->
679        <provider
679-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:29:9-34:54
680            android:name="io.appmetrica.analytics.internal.PreloadInfoContentProvider"
680-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:30:13-87
681            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.appmetrica.preloadinfo.retail"
681-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:31:13-81
682            android:enabled="true"
682-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:32:13-35
683            android:exported="true" />
683-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:33:13-36
684
685        <service
685-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:22:9-25:40
686            android:name="com.google.firebase.sessions.SessionLifecycleService"
686-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:23:13-80
687            android:enabled="true"
687-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:24:13-35
688            android:exported="false" />
688-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:25:13-37
689
690        <provider
690-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
691            android:name="com.google.firebase.provider.FirebaseInitProvider"
691-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
692            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.firebaseinitprovider"
692-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
693            android:directBootAware="true"
693-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
694            android:exported="false"
694-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
695            android:initOrder="100" />
695-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
696
697        <activity
697-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
698            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
698-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
699            android:excludeFromRecents="true"
699-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
700            android:exported="false"
700-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
701            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
701-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
702        <!--
703            Service handling Google Sign-In user revocation. For apps that do not integrate with
704            Google Sign-In, this service will never be started.
705        -->
706        <service
706-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
707            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
707-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
708            android:exported="true"
708-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
709            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
709-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
710            android:visibleToInstantApps="true" />
710-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
711
712        <activity
712-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
713            android:name="com.google.android.gms.common.api.GoogleApiActivity"
713-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
714            android:exported="false"
714-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
715            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
715-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
716
717        <uses-library
717-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
718            android:name="androidx.window.extensions"
718-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
719            android:required="false" />
719-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
720        <uses-library
720-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
721            android:name="androidx.window.sidecar"
721-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
722            android:required="false" />
722-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
723
724        <service
724-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
725            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
725-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
726            android:directBootAware="false"
726-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
727            android:enabled="@bool/enable_system_alarm_service_default"
727-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
728            android:exported="false" />
728-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
729        <service
729-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
730            android:name="androidx.work.impl.background.systemjob.SystemJobService"
730-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
731            android:directBootAware="false"
731-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
732            android:enabled="@bool/enable_system_job_service_default"
732-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
733            android:exported="true"
733-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
734            android:permission="android.permission.BIND_JOB_SERVICE" />
734-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
735        <service
735-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
736            android:name="androidx.work.impl.foreground.SystemForegroundService"
736-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
737            android:directBootAware="false"
737-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
738            android:enabled="@bool/enable_system_foreground_service_default"
738-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
739            android:exported="false" />
739-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
740
741        <receiver
741-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
742            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
742-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
743            android:directBootAware="false"
743-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
744            android:enabled="true"
744-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
745            android:exported="false" />
745-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
746        <receiver
746-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
747            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
747-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
748            android:directBootAware="false"
748-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
749            android:enabled="false"
749-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
750            android:exported="false" >
750-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
751            <intent-filter>
751-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
752                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
752-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
752-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
753                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
753-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
753-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
754            </intent-filter>
755        </receiver>
756        <receiver
756-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
757            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
757-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
758            android:directBootAware="false"
758-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
759            android:enabled="false"
759-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
760            android:exported="false" >
760-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
761            <intent-filter>
761-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
762                <action android:name="android.intent.action.BATTERY_OKAY" />
762-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
762-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
763                <action android:name="android.intent.action.BATTERY_LOW" />
763-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
763-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
764            </intent-filter>
765        </receiver>
766        <receiver
766-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
767            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
767-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
768            android:directBootAware="false"
768-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
769            android:enabled="false"
769-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
770            android:exported="false" >
770-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
771            <intent-filter>
771-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
772                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
772-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
772-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
773                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
773-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
773-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
774            </intent-filter>
775        </receiver>
776        <receiver
776-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
777            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
777-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
778            android:directBootAware="false"
778-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
779            android:enabled="false"
779-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
780            android:exported="false" >
780-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
781            <intent-filter>
781-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
782                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
782-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
782-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
783            </intent-filter>
784        </receiver>
785        <receiver
785-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
786            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
786-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
787            android:directBootAware="false"
787-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
788            android:enabled="false"
788-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
789            android:exported="false" >
789-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
790            <intent-filter>
790-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
791                <action android:name="android.intent.action.BOOT_COMPLETED" />
791-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
791-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
792                <action android:name="android.intent.action.TIME_SET" />
792-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
792-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
793                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
793-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
793-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
794            </intent-filter>
795        </receiver>
796        <receiver
796-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
797            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
797-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
798            android:directBootAware="false"
798-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
799            android:enabled="@bool/enable_system_alarm_service_default"
799-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
800            android:exported="false" >
800-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
801            <intent-filter>
801-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
802                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
802-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
802-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
803            </intent-filter>
804        </receiver>
805        <receiver
805-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
806            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
806-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
807            android:directBootAware="false"
807-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
808            android:enabled="true"
808-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
809            android:exported="true"
809-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
810            android:permission="android.permission.DUMP" >
810-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
811            <intent-filter>
811-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
812                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
812-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
812-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
813            </intent-filter>
814        </receiver>
815
816        <uses-library
816-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
817            android:name="android.ext.adservices"
817-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
818            android:required="false" />
818-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
819
820        <activity
820-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:20:9-24:75
821            android:name="com.facebook.ads.AudienceNetworkActivity"
821-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:21:13-68
822            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
822-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:22:13-106
823            android:exported="false"
823-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:23:13-37
824            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
824-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:24:13-72
825
826        <provider
826-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:26:9-29:40
827            android:name="com.facebook.ads.AudienceNetworkContentProvider"
827-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:27:13-75
828            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.AudienceNetworkContentProvider"
828-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:28:13-82
829            android:exported="false" />
829-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:29:13-37
830
831        <meta-data
831-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
832            android:name="com.google.android.gms.version"
832-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
833            android:value="@integer/google_play_services_version" />
833-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
834
835        <activity
835-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:16:9-20:46
836            android:name="com.vungle.ads.internal.ui.VungleActivity"
836-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:17:13-69
837            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
837-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:18:13-113
838            android:hardwareAccelerated="true"
838-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:19:13-47
839            android:launchMode="singleTop" />
839-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:20:13-43
840
841        <provider
841-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:22:9-26:39
842            android:name="com.vungle.ads.VungleProvider"
842-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:23:13-57
843            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.vungle-provider"
843-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:24:13-67
844            android:exported="false"
844-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:25:13-37
845            android:initOrder="102" />
845-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:26:13-36
846
847        <service
847-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
848            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
848-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
849            android:exported="false" >
849-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
850            <meta-data
850-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
851                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
851-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
852                android:value="cct" />
852-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
853        </service>
854        <service
854-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:24:9-28:63
855            android:name="androidx.room.MultiInstanceInvalidationService"
855-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:25:13-74
856            android:directBootAware="true"
856-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:26:13-43
857            android:exported="false" />
857-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:27:13-37
858
859        <provider
859-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:8:9-11:40
860            android:name="com.squareup.picasso.PicassoProvider"
860-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:9:13-64
861            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.com.squareup.picasso"
861-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:10:13-72
862            android:exported="false" />
862-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:11:13-37
863
864        <activity
864-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:12:9-16:63
865            android:name="com.ironsource.sdk.controller.ControllerActivity"
865-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:13:13-76
866            android:configChanges="orientation|screenSize"
866-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:14:13-59
867            android:hardwareAccelerated="true"
867-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:15:13-47
868            android:theme="@android:style/Theme.NoTitleBar" />
868-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:16:13-60
869        <activity
869-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:17:9-21:75
870            android:name="com.ironsource.sdk.controller.InterstitialActivity"
870-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:18:13-78
871            android:configChanges="orientation|screenSize"
871-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:19:13-59
872            android:hardwareAccelerated="true"
872-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:20:13-47
873            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
873-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:21:13-72
874        <activity
874-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:22:9-26:75
875            android:name="com.ironsource.sdk.controller.OpenUrlActivity"
875-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:23:13-73
876            android:configChanges="orientation|screenSize"
876-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:24:13-59
877            android:hardwareAccelerated="true"
877-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:25:13-47
878            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
878-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:26:13-72
879        <activity
879-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:27:9-36:20
880            android:name="com.ironsource.mediationsdk.testSuite.TestSuiteActivity"
880-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:28:13-83
881            android:configChanges="orientation|screenSize"
881-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:29:13-59
882            android:exported="false"
882-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:30:13-37
883            android:hardwareAccelerated="true"
883-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:31:13-47
884            android:theme="@android:style/Theme.NoTitleBar" >
884-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:32:13-60
885            <meta-data
885-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:33:13-35:40
886                android:name="android.webkit.WebView.EnableSafeBrowsing"
886-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:34:17-73
887                android:value="true" />
887-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:35:17-37
888        </activity>
889
890        <provider
890-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:38:9-41:40
891            android:name="com.ironsource.lifecycle.IronsourceLifecycleProvider"
891-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:39:13-80
892            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.IronsourceLifecycleProvider"
892-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:40:13-79
893            android:exported="false" />
893-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:41:13-37
894        <provider
894-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:42:9-45:40
895            android:name="com.ironsource.lifecycle.LevelPlayActivityLifecycleProvider"
895-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:43:13-87
896            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.LevelPlayActivityLifecycleProvider"
896-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:44:13-86
897            android:exported="false" />
897-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:45:13-37
898
899        <receiver
899-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
900            android:name="androidx.profileinstaller.ProfileInstallReceiver"
900-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
901            android:directBootAware="false"
901-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
902            android:enabled="true"
902-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
903            android:exported="true"
903-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
904            android:permission="android.permission.DUMP" >
904-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
905            <intent-filter>
905-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
906                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
906-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
906-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
907            </intent-filter>
908            <intent-filter>
908-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
909                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
909-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
909-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
910            </intent-filter>
911            <intent-filter>
911-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
912                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
912-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
912-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
913            </intent-filter>
914            <intent-filter>
914-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
915                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
915-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
915-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
916            </intent-filter>
917        </receiver>
918
919        <service
919-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
920            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
920-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
921            android:exported="false"
921-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
922            android:permission="android.permission.BIND_JOB_SERVICE" >
922-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
923        </service>
924
925        <receiver
925-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
926            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
926-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
927            android:exported="false" />
927-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
928
929        <provider
929-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:11:9-14:40
930            android:name="com.adjust.sdk.SystemLifecycleContentProvider"
930-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:12:13-73
931            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.adjust-lifecycle-provider"
931-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:13:13-77
932            android:exported="false" />
932-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:14:13-37
933
934        <activity
934-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:20:9-23:52
935            android:name="sg.bigo.ads.ad.splash.AdSplashActivity"
935-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:21:13-66
936            android:screenOrientation="portrait"
936-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:22:13-49
937            android:theme="@android:style/Theme" />
937-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:23:13-49
938        <activity
938-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:24:9-27:52
939            android:name="sg.bigo.ads.ad.splash.LandscapeAdSplashActivity"
939-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:25:13-75
940            android:screenOrientation="landscape"
940-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:26:13-50
941            android:theme="@android:style/Theme" />
941-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:27:13-49
942
943        <provider
943-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:29:9-32:40
944            android:name="sg.bigo.ads.controller.provider.BigoAdsProvider"
944-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:30:13-75
945            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.BigoAdsProvider"
945-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:31:13-67
946            android:exported="false" />
946-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:32:13-37
947
948        <activity
948-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:34:9-36:55
949            android:name="sg.bigo.ads.controller.form.AdFormActivity"
949-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:35:13-70
950            android:windowSoftInputMode="adjustPan" />
950-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:36:13-52
951        <activity
951-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:37:9-43:20
952            android:name="sg.bigo.ads.api.AdActivity"
952-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:38:13-54
953            android:configChanges="orientation|screenSize"
953-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:39:13-59
954            android:screenOrientation="portrait"
954-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:40:13-49
955            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
955-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:41:13-72
956            android:windowSoftInputMode="stateAlwaysHidden" >
956-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:42:13-60
957        </activity>
958        <activity
958-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:44:9-50:20
959            android:name="sg.bigo.ads.api.PopupAdActivity"
959-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:45:13-59
960            android:configChanges="orientation|screenSize"
960-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:46:13-59
961            android:screenOrientation="portrait"
961-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:47:13-49
962            android:theme="@style/TransparentDialog"
962-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:48:13-53
963            android:windowSoftInputMode="stateAlwaysHidden" >
963-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:49:13-60
964        </activity>
965        <activity
965-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:51:9-57:20
966            android:name="sg.bigo.ads.api.LandingStyleableActivity"
966-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:52:13-68
967            android:configChanges="orientation|screenSize"
967-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:53:13-59
968            android:screenOrientation="behind"
968-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:54:13-47
969            android:theme="@android:style/Theme.Holo.Light.Dialog.NoActionBar"
969-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:55:13-79
970            android:windowSoftInputMode="stateAlwaysHidden" >
970-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:56:13-60
971        </activity>
972        <activity
972-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:58:9-64:20
973            android:name="sg.bigo.ads.api.LandscapeAdActivity"
973-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:59:13-63
974            android:configChanges="orientation|screenSize"
974-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:60:13-59
975            android:screenOrientation="landscape"
975-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:61:13-50
976            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
976-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:62:13-72
977            android:windowSoftInputMode="stateAlwaysHidden" >
977-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:63:13-60
978        </activity>
979        <activity
979-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:65:9-71:20
980            android:name="sg.bigo.ads.api.CompanionAdActivity"
980-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:66:13-63
981            android:configChanges="orientation|screenSize"
981-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:67:13-59
982            android:screenOrientation="portrait"
982-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:68:13-49
983            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
983-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:69:13-72
984            android:windowSoftInputMode="stateAlwaysHidden" >
984-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:70:13-60
985        </activity>
986        <activity
986-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:72:9-78:20
987            android:name="sg.bigo.ads.api.LandscapeCompanionAdActivity"
987-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:73:13-72
988            android:configChanges="orientation|screenSize"
988-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:74:13-59
989            android:screenOrientation="landscape"
989-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:75:13-50
990            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
990-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:76:13-72
991            android:windowSoftInputMode="stateAlwaysHidden" >
991-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:77:13-60
992        </activity>
993        <activity
993-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:79:9-83:74
994            android:name="sg.bigo.ads.core.mraid.MraidVideoActivity"
994-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:80:13-69
995            android:configChanges="keyboardHidden|orientation|screenSize"
995-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:81:13-74
996            android:screenOrientation="portrait"
996-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:82:13-49
997            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
997-->[com.bigossp:bigo-ads:5.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa15b0c96a86cc746b6a138b51f3b47\transformed\jetified-bigo-ads-5.4.0\AndroidManifest.xml:83:13-71
998        <activity
998-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:21:9-25:52
999            android:name="com.fyber.inneractive.sdk.activities.InneractiveInternalBrowserActivity"
999-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:22:13-99
1000            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
1000-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:23:13-106
1001            android:hardwareAccelerated="true"
1001-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:24:13-47
1002            android:screenOrientation="fullUser" />
1002-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:25:13-49
1003        <activity
1003-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:26:9-30:74
1004            android:name="com.fyber.inneractive.sdk.activities.InneractiveFullscreenAdActivity"
1004-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:27:13-96
1005            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
1005-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:28:13-106
1006            android:hardwareAccelerated="true"
1006-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:29:13-47
1007            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
1007-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:30:13-71
1008        <activity
1008-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:31:9-35:74
1009            android:name="com.fyber.inneractive.sdk.activities.InneractiveRichMediaVideoPlayerActivityCore"
1009-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:32:13-108
1010            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
1010-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:33:13-106
1011            android:hardwareAccelerated="true"
1011-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:34:13-47
1012            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
1012-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:35:13-71
1013        <activity
1013-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:36:9-41:75
1014            android:name="com.fyber.inneractive.sdk.activities.InternalStoreWebpageActivity"
1014-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:37:13-93
1015            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
1015-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:38:13-106
1016            android:excludeFromRecents="true"
1016-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:39:13-46
1017            android:screenOrientation="sensor"
1017-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:40:13-47
1018            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
1018-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:41:13-72
1019        <activity
1019-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:42:9-46:52
1020            android:name="com.fyber.inneractive.sdk.activities.FyberReportAdActivity"
1020-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:43:13-86
1021            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
1021-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:44:13-106
1022            android:hardwareAccelerated="true"
1022-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:45:13-47
1023            android:screenOrientation="fullUser" /> <!-- mbridge base activity -->
1023-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:46:13-49
1024        <activity
1024-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:23:9-28:80
1025            android:name="com.mbridge.msdk.activity.MBCommonActivity"
1025-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:24:13-70
1026            android:configChanges="keyboard|orientation"
1026-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:25:13-57
1027            android:excludeFromRecents="true"
1027-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:26:13-46
1028            android:exported="false"
1028-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:27:13-37
1029            android:theme="@style/mbridge_transparent_common_activity_style" /> <!-- integration rewardVideo if aggregation nativeX pls add start -->
1029-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:28:13-77
1030        <activity android:name="com.mbridge.msdk.out.LoadingActivity" /> <!-- integration rewardVideo if aggregation nativeX pls add end -->
1030-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:31:9-73
1030-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:31:19-70
1031        <activity
1031-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:33:9-37:75
1032            android:name="com.mbridge.msdk.newreward.player.MBRewardVideoActivity"
1032-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:34:13-83
1033            android:configChanges="orientation|keyboardHidden|screenSize"
1033-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:35:13-74
1034            android:excludeFromRecents="true"
1034-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:36:13-46
1035            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
1035-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:37:13-72
1036        <activity
1036-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:38:9-42:75
1037            android:name="com.mbridge.msdk.reward.player.MBRewardVideoActivity"
1037-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:39:13-80
1038            android:configChanges="orientation|keyboardHidden|screenSize"
1038-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:40:13-74
1039            android:excludeFromRecents="true"
1039-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:41:13-46
1040            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
1040-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:42:13-72
1041
1042        <receiver
1042-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:44:9-50:20
1043            android:name="com.mbridge.msdk.foundation.same.broadcast.NetWorkChangeReceiver"
1043-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:45:13-92
1044            android:exported="true" >
1044-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:46:13-36
1045            <intent-filter>
1045-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
1046                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
1046-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
1046-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
1047            </intent-filter>
1048        </receiver>
1049    </application>
1050
1051</manifest>
