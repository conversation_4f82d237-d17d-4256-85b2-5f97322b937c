package com.tqhit.battery.one.features.emoji.presentation.overlay;

import android.content.Context;
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class EmojiAccessibilityServiceManager_Factory implements Factory<EmojiAccessibilityServiceManager> {
  private final Provider<Context> contextProvider;

  private final Provider<CustomizationRepository> customizationRepositoryProvider;

  public EmojiAccessibilityServiceManager_Factory(Provider<Context> contextProvider,
      Provider<CustomizationRepository> customizationRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.customizationRepositoryProvider = customizationRepositoryProvider;
  }

  @Override
  public EmojiAccessibilityServiceManager get() {
    return newInstance(contextProvider.get(), customizationRepositoryProvider.get());
  }

  public static EmojiAccessibilityServiceManager_Factory create(Provider<Context> contextProvider,
      Provider<CustomizationRepository> customizationRepositoryProvider) {
    return new EmojiAccessibilityServiceManager_Factory(contextProvider, customizationRepositoryProvider);
  }

  public static EmojiAccessibilityServiceManager newInstance(Context context,
      CustomizationRepository customizationRepository) {
    return new EmojiAccessibilityServiceManager(context, customizationRepository);
  }
}
