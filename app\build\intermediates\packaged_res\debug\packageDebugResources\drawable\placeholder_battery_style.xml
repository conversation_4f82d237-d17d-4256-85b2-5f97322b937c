<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="120dp"
    android:height="180dp"
    android:viewportWidth="120"
    android:viewportHeight="180">
  
  <!-- Background -->
  <path
      android:fillColor="?attr/colorSurfaceVariant"
      android:pathData="M0,0h120v180h-120z"/>
  
  <!-- Battery outline -->
  <path
      android:fillColor="?attr/colorOnSurfaceVariant"
      android:pathData="M40,60h40v60h-40z"
      android:strokeWidth="2"
      android:strokeColor="?attr/colorOnSurfaceVariant"/>
  
  <!-- Battery top -->
  <path
      android:fillColor="?attr/colorOnSurfaceVariant"
      android:pathData="M50,55h20v5h-20z"/>
  
  <!-- Emoji placeholder circle -->
  <path
      android:fillColor="?attr/colorOnSurfaceVariant"
      android:pathData="M60,90m-15,0a15,15 0,1 1,30 0a15,15 0,1 1,-30 0"/>
  
  <!-- Percentage placeholder -->
  <path
      android:fillColor="?attr/colorOnSurfaceVariant"
      android:pathData="M45,140h30v8h-30z"/>
</vector>
