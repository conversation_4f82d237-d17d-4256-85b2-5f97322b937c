package com.tqhit.battery.one.features.emoji.data.repository;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CustomizationRepositoryImpl_Factory implements Factory<CustomizationRepositoryImpl> {
  private final Provider<Context> contextProvider;

  public CustomizationRepositoryImpl_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public CustomizationRepositoryImpl get() {
    return newInstance(contextProvider.get());
  }

  public static CustomizationRepositoryImpl_Factory create(Provider<Context> contextProvider) {
    return new CustomizationRepositoryImpl_Factory(contextProvider);
  }

  public static CustomizationRepositoryImpl newInstance(Context context) {
    return new CustomizationRepositoryImpl(context);
  }
}
