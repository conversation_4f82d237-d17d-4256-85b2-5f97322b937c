package com.tqhit.battery.one.features.stats.discharge.presentation;

import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager;
import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager;
import com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DischargeFragment_MembersInjector implements MembersInjector<DischargeFragment> {
  private final Provider<InfoButtonManager> infoButtonManagerProvider;

  private final Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider;

  private final Provider<TimeConverter> timeConverterProvider;

  private final Provider<AppLifecycleManager> appLifecycleManagerProvider;

  public DischargeFragment_MembersInjector(Provider<InfoButtonManager> infoButtonManagerProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider,
      Provider<TimeConverter> timeConverterProvider,
      Provider<AppLifecycleManager> appLifecycleManagerProvider) {
    this.infoButtonManagerProvider = infoButtonManagerProvider;
    this.applovinNativeAdManagerProvider = applovinNativeAdManagerProvider;
    this.timeConverterProvider = timeConverterProvider;
    this.appLifecycleManagerProvider = appLifecycleManagerProvider;
  }

  public static MembersInjector<DischargeFragment> create(
      Provider<InfoButtonManager> infoButtonManagerProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider,
      Provider<TimeConverter> timeConverterProvider,
      Provider<AppLifecycleManager> appLifecycleManagerProvider) {
    return new DischargeFragment_MembersInjector(infoButtonManagerProvider, applovinNativeAdManagerProvider, timeConverterProvider, appLifecycleManagerProvider);
  }

  @Override
  public void injectMembers(DischargeFragment instance) {
    injectInfoButtonManager(instance, infoButtonManagerProvider.get());
    injectApplovinNativeAdManager(instance, applovinNativeAdManagerProvider.get());
    injectTimeConverter(instance, timeConverterProvider.get());
    injectAppLifecycleManager(instance, appLifecycleManagerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.infoButtonManager")
  public static void injectInfoButtonManager(DischargeFragment instance,
      InfoButtonManager infoButtonManager) {
    instance.infoButtonManager = infoButtonManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.applovinNativeAdManager")
  public static void injectApplovinNativeAdManager(DischargeFragment instance,
      ApplovinNativeAdManager applovinNativeAdManager) {
    instance.applovinNativeAdManager = applovinNativeAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.timeConverter")
  public static void injectTimeConverter(DischargeFragment instance, TimeConverter timeConverter) {
    instance.timeConverter = timeConverter;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.appLifecycleManager")
  public static void injectAppLifecycleManager(DischargeFragment instance,
      AppLifecycleManager appLifecycleManager) {
    instance.appLifecycleManager = appLifecycleManager;
  }
}
