package com.tqhit.battery.one.viewmodel;

import com.tqhit.battery.one.repository.AppRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppViewModel_Factory implements Factory<AppViewModel> {
  private final Provider<AppRepository> appRepositoryProvider;

  public AppViewModel_Factory(Provider<AppRepository> appRepositoryProvider) {
    this.appRepositoryProvider = appRepositoryProvider;
  }

  @Override
  public AppViewModel get() {
    return newInstance(appRepositoryProvider.get());
  }

  public static AppViewModel_Factory create(Provider<AppRepository> appRepositoryProvider) {
    return new AppViewModel_Factory(appRepositoryProvider);
  }

  public static AppViewModel newInstance(AppRepository appRepository) {
    return new AppViewModel(appRepository);
  }
}
