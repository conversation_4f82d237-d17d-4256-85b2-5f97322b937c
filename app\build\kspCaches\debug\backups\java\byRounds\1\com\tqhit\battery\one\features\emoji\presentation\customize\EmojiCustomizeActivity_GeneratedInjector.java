package com.tqhit.battery.one.features.emoji.presentation.customize;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;
import javax.annotation.processing.Generated;

@OriginatingElement(
    topLevelClass = EmojiCustomizeActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
@Generated("dagger.hilt.android.processor.internal.androidentrypoint.InjectorEntryPointGenerator")
public interface EmojiCustomizeActivity_GeneratedInjector {
  void injectEmojiCustomizeActivity(EmojiCustomizeActivity emojiCustomizeActivity);
}
