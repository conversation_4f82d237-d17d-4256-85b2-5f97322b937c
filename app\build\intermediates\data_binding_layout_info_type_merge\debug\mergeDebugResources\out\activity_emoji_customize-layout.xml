<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_emoji_customize" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\activity_emoji_customize.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_emoji_customize_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="546" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="42"/></Target><Target id="@+id/bannerAdContainer" view="FrameLayout"><Expressions/><location startLine="22" startOffset="4" endLine="30" endOffset="55"/></Target><Target id="@+id/scrollView" view="ScrollView"><Expressions/><location startLine="33" startOffset="4" endLine="529" endOffset="16"/></Target><Target id="@+id/customizeSubtitle" view="TextView"><Expressions/><location startLine="61" startOffset="16" endLine="69" endOffset="41"/></Target><Target id="@+id/customizeInfo" view="ImageView"><Expressions/><location startLine="71" startOffset="16" endLine="80" endOffset="50"/></Target><Target id="@+id/globalToggleSwitch" view="Switch"><Expressions/><location startLine="117" startOffset="16" endLine="121" endOffset="55"/></Target><Target id="@+id/previewContainer" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="143" startOffset="16" endLine="198" endOffset="67"/></Target><Target id="@+id/previewContent" view="RelativeLayout"><Expressions/><location startLine="152" startOffset="20" endLine="197" endOffset="36"/></Target><Target id="@+id/previewPercentage" view="TextView"><Expressions/><location startLine="162" startOffset="24" endLine="172" endOffset="62"/></Target><Target id="@+id/previewBattery" view="ImageView"><Expressions/><location startLine="182" startOffset="28" endLine="187" endOffset="70"/></Target><Target id="@+id/previewEmoji" view="ImageView"><Expressions/><location startLine="190" startOffset="28" endLine="195" endOffset="68"/></Target><Target id="@+id/previewLevelSlider" view="SeekBar"><Expressions/><location startLine="215" startOffset="20" endLine="221" endOffset="47"/></Target><Target id="@+id/previewLevelText" view="TextView"><Expressions/><location startLine="223" startOffset="20" endLine="232" endOffset="50"/></Target><Target id="@+id/batteryStylesRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="255" startOffset="16" endLine="262" endOffset="90"/></Target><Target id="@+id/emojiStylesRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="274" startOffset="16" endLine="280" endOffset="90"/></Target><Target id="@+id/showEmojiSwitch" view="Switch"><Expressions/><location startLine="317" startOffset="20" endLine="321" endOffset="48"/></Target><Target id="@+id/showPercentageSwitch" view="Switch"><Expressions/><location startLine="339" startOffset="20" endLine="343" endOffset="48"/></Target><Target id="@+id/fontSizeSlider" view="SeekBar"><Expressions/><location startLine="390" startOffset="20" endLine="396" endOffset="46"/></Target><Target id="@+id/fontSizeValue" view="TextView"><Expressions/><location startLine="406" startOffset="20" endLine="415" endOffset="50"/></Target><Target id="@+id/emojiScaleSlider" view="SeekBar"><Expressions/><location startLine="442" startOffset="20" endLine="448" endOffset="46"/></Target><Target id="@+id/emojiScaleValue" view="TextView"><Expressions/><location startLine="458" startOffset="20" endLine="467" endOffset="50"/></Target><Target id="@+id/loadingContainer" view="FrameLayout"><Expressions/><location startLine="472" startOffset="12" endLine="492" endOffset="25"/></Target><Target id="@+id/errorContainer" view="LinearLayout"><Expressions/><location startLine="495" startOffset="12" endLine="526" endOffset="26"/></Target><Target id="@+id/errorMessage" view="TextView"><Expressions/><location startLine="510" startOffset="16" endLine="518" endOffset="45"/></Target><Target id="@+id/retryButton" view="Button"><Expressions/><location startLine="520" startOffset="16" endLine="525" endOffset="50"/></Target><Target id="@+id/applyButton" view="Button"><Expressions/><location startLine="532" startOffset="4" endLine="544" endOffset="55"/></Target></Targets></Layout>