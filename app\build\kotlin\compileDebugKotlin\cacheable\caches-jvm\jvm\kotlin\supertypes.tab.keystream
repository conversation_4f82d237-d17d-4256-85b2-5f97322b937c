(com.tqhit.battery.one.BatteryApplication:com.tqhit.battery.one.activity.animation.AnimationActivity2com.tqhit.battery.one.activity.debug.DebugActivity0com.tqhit.battery.one.activity.main.MainActivityCcom.tqhit.battery.one.activity.onboarding.LanguageSelectionActivity>com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity=com.tqhit.battery.one.activity.password.EnterPasswordActivity4com.tqhit.battery.one.activity.splash.SplashActivity8com.tqhit.battery.one.activity.starting.StartingActivity;com.tqhit.battery.one.activity.starting.StartingViewAdapter7com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager6com.tqhit.battery.one.ads.core.ApplovinBannerAdManager<com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager6com.tqhit.battery.one.ads.core.ApplovinNativeAdManager8com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager.com.tqhit.battery.one.base.LocaleAwareActivity<com.tqhit.battery.one.component.progress.VerticalProgressBar;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog>com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog:com.tqhit.battery.one.dialog.capacity.ChangeCapacityDialog9com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog:com.tqhit.battery.one.dialog.language.SelectLanguageDialogBcom.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog4com.tqhit.battery.one.dialog.theme.SelectColorDialog4com.tqhit.battery.one.dialog.theme.SelectThemeDialog0com.tqhit.battery.one.dialog.utils.LoadingDialog5com.tqhit.battery.one.dialog.utils.NotificationDialogGcom.tqhit.battery.one.features.navigation.AppNavigator.NavigationMethod;com.tqhit.battery.one.features.navigation.StateChangeReasonCcom.tqhit.battery.one.features.navigation.SharedNavigationViewModelUcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapterccom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter.AppViewHolderecom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter.AppDiffCallbackTcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogGcom.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCacheLcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragmentMcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModelScom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepositoryWcom.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProviderPcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsServiceMcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCacheMcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCacheIcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager>com.tqhit.battery.one.features.stats.discharge.domain.AppStateYcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.NoValidationNeededLcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.ValidPcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.CorrectedDcom.tqhit.battery.one.features.stats.discharge.domain.CorrectionTypeHcom.tqhit.battery.one.features.stats.discharge.domain.CorrectionStrategyMcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragmentNcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModelTcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceDcom.tqhit.battery.one.features.stats.health.cache.DefaultHealthCacheEcom.tqhit.battery.one.features.stats.health.data.HealthChartTimeRangeFcom.tqhit.battery.one.features.stats.health.data.HealthCalculationModeHcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModelNcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepositoryTcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService/<EMAIL>@com.tqhit.battery.one.fragment.main.others.adapter.OthersAdapterQcom.tqhit.battery.one.fragment.main.others.adapter.OthersAdapter.OthersViewHolderScom.tqhit.battery.one.fragment.main.others.adapter.OthersAdapter.OthersDiffCallbackGcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionUiState.IdleScom.tqhit.battery.one.fragment.onboarding.LanguageSelectionUiState.LanguageSelectedTcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionUiState.LanguageConfirmedYcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionUiState.NavigatingToOnboardingOcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionEvent.SelectLanguageYcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionEvent.ConfirmLanguageSelectionTcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionEvent.ProceedToOnboardingFcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionEvent.ResetDcom.tqhit.battery.one.fragment.onboarding.LanguageSelectionViewModel9com.tqhit.battery.one.manager.graph.BatteryHistoryManager=<EMAIL>;com.tqhit.battery.one.repository.PreloadingResult.AllFailed7com.tqhit.battery.one.repository.PreloadingResult.Error?<EMAIL>@com.tqhit.battery.one.repository.ThumbnailPreloadingResult.Error4com.tqhit.battery.one.service.ChargingOverlayService,com.tqhit.battery.one.viewmodel.AppViewModel<com.tqhit.battery.one.viewmodel.animation.AnimationViewModel8com.tqhit.battery.one.viewmodel.battery.BatteryViewModelPcom.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl>com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleDcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigEcom.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig;com.tqhit.battery.one.features.emoji.domain.model.EmojiItemCcom.tqhit.battery.one.features.emoji.domain.model.EmojiCustomFieldsFcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryMcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragmentZcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.LoadInitialData^com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.InitializeWithStyle]com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.SelectBatteryStyle[com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.SelectEmojiStyle^com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ToggleGlobalEnabledZcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ToggleShowEmoji_com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ToggleShowPercentageccom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.UpdatePercentageFontSize_com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.UpdateEmojiSizeScale`com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.UpdatePercentageColorZcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ShowColorPickerZcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.HideColorPickerVcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.SelectColordcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.UpdatePreviewBatteryLevelYcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.RefreshPreview]com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ApplyCustomizationZcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ResetToDefaultsWcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.NavigateBack_com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ClearNavigationStateScom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.OnResumeRcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.OnPauseTcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.RetryLoadUcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ClearErrorWcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ValidateForm^com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ShowValidationErrorNcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelRcom.tqhit.battery.one.features.emoji.presentation.customize.EmojiCustomizeActivity`com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryComponentDiffCallback[com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryComponentAdapter^com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryComponentViewHolder^com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiComponentDiffCallbackYcom.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiComponentAdapter\com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiComponentViewHolder]com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.LoadInitialDataYcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.RefreshDataWcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.RetryLoad\com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SelectCategory`com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SearchQueryChanged^com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ToggleSearchModeYcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ClearSearch`com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SelectBatteryStyleacom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ToggleGlobalFeatureacom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.TogglePremiumFilter^com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ToggleFreeFilter]com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ClearAllFilters`com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UnlockPremiumStyle]com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.WatchAdToUnlockccom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.PurchasePremiumUnlockUcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEventbcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEvent.LoadBannerAdhcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEvent.LoadInterstitialAdhcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEvent.ShowInterstitialAdbcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEvent.AdLoadFailed]com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEvent.AdShown^com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.AdEvent.AdClosed]com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigationEventqcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigationEvent.NavigateToCustomizepcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigationEvent.NavigateToSettingsjcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigationEvent.NavigateBackrcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigationEvent.ShowPermissionDialogocom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigationEvent.ShowPremiumDialogXcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ErrorEventbcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ErrorEvent.ShowErrorecom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ErrorEvent.DismissErrorbcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ErrorEvent.ShowToastUcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UIEventdcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UIEvent.ShowInfoDialoggcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UIEvent.DismissInfoDialogecom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UIEvent.ShowSortOptionshcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UIEvent.DismissSortOptions\com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UIEvent.SortByVcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SortTypeYcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SystemEventbcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SystemEvent.OnResumeacom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SystemEvent.OnPauseccom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SystemEvent.OnDestroylcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SystemEvent.OnPermissionResultjcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SystemEvent.OnActivityResultCcom.tqhit.battery.one.features.emoji.presentation.gallery.ErrorType]com.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent.NavigateToCustomize[com.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent.ShowPremiumDialog^com.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent.ShowPermissionDialogXcom.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent.ShowInfoDialogScom.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent.ShowToastQcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelNcom.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragmentZcom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleDiffCallbackUcom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterXcom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleViewHolderVcom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.CategoryDiffCallbackQcom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.CategoryAdapterTcom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.CategoryViewHolder[com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.GridSpacingItemDecorationZcom.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityServiceJcom.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryViewXcom.tqhit.battery.one.fragment.main.others.adapter.OthersAdapter.OthersViewHolder.NormalZcom.tqhit.battery.one.fragment.main.others.adapter.OthersAdapter.OthersViewHolder.NativeAdEcom.tqhit.battery.one.fragment.main.others.data.OthersItemData.NormalGcom.tqhit.battery.one.fragment.main.others.data.OthersItemData.NativeAd.com.tqhit.battery.one.ui.custom.SquareCardView                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            