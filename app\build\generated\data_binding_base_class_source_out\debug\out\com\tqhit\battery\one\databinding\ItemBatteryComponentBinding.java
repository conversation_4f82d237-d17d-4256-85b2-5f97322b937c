// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBatteryComponentBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView batteryImage;

  @NonNull
  public final CardView componentContainer;

  @NonNull
  public final ImageButton lockBtn;

  @NonNull
  public final View selectionIndicator;

  @NonNull
  public final ShimmerFrameLayout shimmerLayout;

  private ItemBatteryComponentBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageView batteryImage, @NonNull CardView componentContainer,
      @NonNull ImageButton lockBtn, @NonNull View selectionIndicator,
      @NonNull ShimmerFrameLayout shimmerLayout) {
    this.rootView = rootView;
    this.batteryImage = batteryImage;
    this.componentContainer = componentContainer;
    this.lockBtn = lockBtn;
    this.selectionIndicator = selectionIndicator;
    this.shimmerLayout = shimmerLayout;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBatteryComponentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBatteryComponentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_battery_component, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBatteryComponentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.batteryImage;
      ImageView batteryImage = ViewBindings.findChildViewById(rootView, id);
      if (batteryImage == null) {
        break missingId;
      }

      id = R.id.componentContainer;
      CardView componentContainer = ViewBindings.findChildViewById(rootView, id);
      if (componentContainer == null) {
        break missingId;
      }

      id = R.id.lockBtn;
      ImageButton lockBtn = ViewBindings.findChildViewById(rootView, id);
      if (lockBtn == null) {
        break missingId;
      }

      id = R.id.selectionIndicator;
      View selectionIndicator = ViewBindings.findChildViewById(rootView, id);
      if (selectionIndicator == null) {
        break missingId;
      }

      id = R.id.shimmerLayout;
      ShimmerFrameLayout shimmerLayout = ViewBindings.findChildViewById(rootView, id);
      if (shimmerLayout == null) {
        break missingId;
      }

      return new ItemBatteryComponentBinding((ConstraintLayout) rootView, batteryImage,
          componentContainer, lockBtn, selectionIndicator, shimmerLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
