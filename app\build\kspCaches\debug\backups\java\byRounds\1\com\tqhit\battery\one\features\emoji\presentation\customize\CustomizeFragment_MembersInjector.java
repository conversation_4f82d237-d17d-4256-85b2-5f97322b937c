package com.tqhit.battery.one.features.emoji.presentation.customize;

import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CustomizeFragment_MembersInjector implements MembersInjector<CustomizeFragment> {
  private final Provider<AppRepository> appRepositoryProvider;

  public CustomizeFragment_MembersInjector(Provider<AppRepository> appRepositoryProvider) {
    this.appRepositoryProvider = appRepositoryProvider;
  }

  public static MembersInjector<CustomizeFragment> create(
      Provider<AppRepository> appRepositoryProvider) {
    return new CustomizeFragment_MembersInjector(appRepositoryProvider);
  }

  @Override
  public void injectMembers(CustomizeFragment instance) {
    injectAppRepository(instance, appRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment.appRepository")
  public static void injectAppRepository(CustomizeFragment instance, AppRepository appRepository) {
    instance.appRepository = appRepository;
  }
}
