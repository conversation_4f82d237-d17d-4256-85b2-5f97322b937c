package com.tqhit.battery.one.repository;

import android.content.Context;
import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppRepository_Factory implements Factory<AppRepository> {
  private final Provider<PreferencesHelper> preferencesHelperProvider;

  private final Provider<Context> contextProvider;

  public AppRepository_Factory(Provider<PreferencesHelper> preferencesHelperProvider,
      Provider<Context> contextProvider) {
    this.preferencesHelperProvider = preferencesHelperProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public AppRepository get() {
    return newInstance(preferencesHelperProvider.get(), contextProvider.get());
  }

  public static AppRepository_Factory create(Provider<PreferencesHelper> preferencesHelperProvider,
      Provider<Context> contextProvider) {
    return new AppRepository_Factory(preferencesHelperProvider, contextProvider);
  }

  public static AppRepository newInstance(PreferencesHelper preferencesHelper, Context context) {
    return new AppRepository(preferencesHelper, context);
  }
}
