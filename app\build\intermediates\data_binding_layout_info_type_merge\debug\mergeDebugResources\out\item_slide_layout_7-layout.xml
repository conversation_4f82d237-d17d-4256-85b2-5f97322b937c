<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_slide_layout_7" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_slide_layout_7.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_slide_layout_7_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="377" endOffset="14"/></Target><Target id="@+id/indent_top" view="LinearLayout"><Expressions/><location startLine="19" startOffset="12" endLine="23" endOffset="53"/></Target><Target id="@+id/main" view="LinearLayout"><Expressions/><location startLine="24" startOffset="12" endLine="256" endOffset="26"/></Target><Target id="@+id/textView4" view="TextView"><Expressions/><location startLine="34" startOffset="16" endLine="48" endOffset="52"/></Target><Target id="@+id/text5" view="LinearLayout"><Expressions/><location startLine="49" startOffset="16" endLine="72" endOffset="30"/></Target><Target id="@+id/device_name" view="TextView"><Expressions/><location startLine="97" startOffset="24" endLine="113" endOffset="60"/></Target><Target id="@+id/discharging" view="TextView"><Expressions/><location startLine="132" startOffset="24" endLine="148" endOffset="60"/></Target><Target id="@+id/polarity" view="TextView"><Expressions/><location startLine="167" startOffset="24" endLine="183" endOffset="60"/></Target><Target id="@+id/parameter" view="TextView"><Expressions/><location startLine="202" startOffset="24" endLine="218" endOffset="60"/></Target><Target id="@+id/capacity" view="TextView"><Expressions/><location startLine="237" startOffset="24" endLine="253" endOffset="60"/></Target><Target id="@+id/change_capacity" view="Button"><Expressions/><location startLine="297" startOffset="20" endLine="309" endOffset="74"/></Target><Target id="@+id/text_view_reset_charge" view="TextView"><Expressions/><location startLine="310" startOffset="20" endLine="323" endOffset="56"/></Target><Target id="@+id/progressbar2" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="328" startOffset="4" endLine="343" endOffset="33"/></Target><Target id="@+id/next_page" view="LinearLayout"><Expressions/><location startLine="344" startOffset="4" endLine="375" endOffset="18"/></Target><Target id="@+id/swipe_text" view="TextView"><Expressions/><location startLine="359" startOffset="8" endLine="368" endOffset="40"/></Target></Targets></Layout>