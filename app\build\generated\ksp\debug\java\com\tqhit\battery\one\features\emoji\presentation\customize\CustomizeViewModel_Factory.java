package com.tqhit.battery.one.features.emoji.presentation.customize;

import com.tqhit.battery.one.features.emoji.data.service.EmojiItemService;
import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase;
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CustomizeViewModel_Factory implements Factory<CustomizeViewModel> {
  private final Provider<LoadCustomizationUseCase> loadCustomizationUseCaseProvider;

  private final Provider<SaveCustomizationUseCase> saveCustomizationUseCaseProvider;

  private final Provider<EmojiItemService> emojiItemServiceProvider;

  public CustomizeViewModel_Factory(
      Provider<LoadCustomizationUseCase> loadCustomizationUseCaseProvider,
      Provider<SaveCustomizationUseCase> saveCustomizationUseCaseProvider,
      Provider<EmojiItemService> emojiItemServiceProvider) {
    this.loadCustomizationUseCaseProvider = loadCustomizationUseCaseProvider;
    this.saveCustomizationUseCaseProvider = saveCustomizationUseCaseProvider;
    this.emojiItemServiceProvider = emojiItemServiceProvider;
  }

  @Override
  public CustomizeViewModel get() {
    return newInstance(loadCustomizationUseCaseProvider.get(), saveCustomizationUseCaseProvider.get(), emojiItemServiceProvider.get());
  }

  public static CustomizeViewModel_Factory create(
      Provider<LoadCustomizationUseCase> loadCustomizationUseCaseProvider,
      Provider<SaveCustomizationUseCase> saveCustomizationUseCaseProvider,
      Provider<EmojiItemService> emojiItemServiceProvider) {
    return new CustomizeViewModel_Factory(loadCustomizationUseCaseProvider, saveCustomizationUseCaseProvider, emojiItemServiceProvider);
  }

  public static CustomizeViewModel newInstance(LoadCustomizationUseCase loadCustomizationUseCase,
      SaveCustomizationUseCase saveCustomizationUseCase, EmojiItemService emojiItemService) {
    return new CustomizeViewModel(loadCustomizationUseCase, saveCustomizationUseCase, emojiItemService);
  }
}
