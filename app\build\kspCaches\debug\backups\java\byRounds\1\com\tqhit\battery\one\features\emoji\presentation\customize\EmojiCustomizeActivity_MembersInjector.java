package com.tqhit.battery.one.features.emoji.presentation.customize;

import com.tqhit.battery.one.base.LocaleAwareActivity_MembersInjector;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class EmojiCustomizeActivity_MembersInjector implements MembersInjector<EmojiCustomizeActivity> {
  private final Provider<AppRepository> appRepositoryProvider;

  private final Provider<AppRepository> appRepositoryProvider2;

  public EmojiCustomizeActivity_MembersInjector(Provider<AppRepository> appRepositoryProvider,
      Provider<AppRepository> appRepositoryProvider2) {
    this.appRepositoryProvider = appRepositoryProvider;
    this.appRepositoryProvider2 = appRepositoryProvider2;
  }

  public static MembersInjector<EmojiCustomizeActivity> create(
      Provider<AppRepository> appRepositoryProvider,
      Provider<AppRepository> appRepositoryProvider2) {
    return new EmojiCustomizeActivity_MembersInjector(appRepositoryProvider, appRepositoryProvider2);
  }

  @Override
  public void injectMembers(EmojiCustomizeActivity instance) {
    LocaleAwareActivity_MembersInjector.injectAppRepository(instance, appRepositoryProvider.get());
    injectAppRepository(instance, appRepositoryProvider2.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.emoji.presentation.customize.EmojiCustomizeActivity.appRepository")
  public static void injectAppRepository(EmojiCustomizeActivity instance,
      AppRepository appRepository) {
    instance.appRepository = appRepository;
  }
}
