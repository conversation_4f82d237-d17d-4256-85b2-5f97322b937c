package com.tqhit.battery.one.service;

import com.tqhit.battery.one.repository.AnimationRepository;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ChargingOverlayService_MembersInjector implements MembersInjector<ChargingOverlayService> {
  private final Provider<AppRepository> appRepositoryProvider;

  private final Provider<AnimationRepository> animationRepositoryProvider;

  public ChargingOverlayService_MembersInjector(Provider<AppRepository> appRepositoryProvider,
      Provider<AnimationRepository> animationRepositoryProvider) {
    this.appRepositoryProvider = appRepositoryProvider;
    this.animationRepositoryProvider = animationRepositoryProvider;
  }

  public static MembersInjector<ChargingOverlayService> create(
      Provider<AppRepository> appRepositoryProvider,
      Provider<AnimationRepository> animationRepositoryProvider) {
    return new ChargingOverlayService_MembersInjector(appRepositoryProvider, animationRepositoryProvider);
  }

  @Override
  public void injectMembers(ChargingOverlayService instance) {
    injectAppRepository(instance, appRepositoryProvider.get());
    injectAnimationRepository(instance, animationRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.service.ChargingOverlayService.appRepository")
  public static void injectAppRepository(ChargingOverlayService instance,
      AppRepository appRepository) {
    instance.appRepository = appRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.service.ChargingOverlayService.animationRepository")
  public static void injectAnimationRepository(ChargingOverlayService instance,
      AnimationRepository animationRepository) {
    instance.animationRepository = animationRepository;
  }
}
