package com.tqhit.battery.one.features.stats.discharge.repository;

import android.content.Context;
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class BatteryRepository_Factory implements Factory<BatteryRepository> {
  private final Provider<Context> contextProvider;

  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  private final Provider<DischargeRatesCache> dischargeRatesCacheProvider;

  private final Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider;

  public BatteryRepository_Factory(Provider<Context> contextProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<AppRepository> appRepositoryProvider,
      Provider<DischargeRatesCache> dischargeRatesCacheProvider,
      Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
    this.appRepositoryProvider = appRepositoryProvider;
    this.dischargeRatesCacheProvider = dischargeRatesCacheProvider;
    this.dischargeSessionRepositoryProvider = dischargeSessionRepositoryProvider;
  }

  @Override
  public BatteryRepository get() {
    return newInstance(contextProvider.get(), coreBatteryStatsProvider.get(), appRepositoryProvider.get(), dischargeRatesCacheProvider.get(), dischargeSessionRepositoryProvider.get());
  }

  public static BatteryRepository_Factory create(Provider<Context> contextProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<AppRepository> appRepositoryProvider,
      Provider<DischargeRatesCache> dischargeRatesCacheProvider,
      Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider) {
    return new BatteryRepository_Factory(contextProvider, coreBatteryStatsProvider, appRepositoryProvider, dischargeRatesCacheProvider, dischargeSessionRepositoryProvider);
  }

  public static BatteryRepository newInstance(Context context,
      CoreBatteryStatsProvider coreBatteryStatsProvider, AppRepository appRepository,
      DischargeRatesCache dischargeRatesCache,
      DischargeSessionRepository dischargeSessionRepository) {
    return new BatteryRepository(context, coreBatteryStatsProvider, appRepository, dischargeRatesCache, dischargeSessionRepository);
  }
}
