<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:elevation="4dp"
    android:background="@drawable/block_card_ads">
    <FrameLayout
        android:id="@+id/media_view_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="10dp"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/contentContainer"
        android:layout_width="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_height="140dp"
        android:padding="16dp">


        <LinearLayout
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/black"
                android:maxLines="2"
                android:textSize="24sp"
                android:textStyle="bold" />
            <TextView
                android:id="@+id/advertiser_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="?attr/black"/>

            <TextView
                android:id="@+id/body_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/itemTitle"
                android:maxLines="3"
                android:layout_marginTop="4dp"
                android:textColor="?attr/black"
                android:textSize="14sp" />

        </LinearLayout>

        <ImageView
            android:layout_weight="1"
            android:id="@+id/icon_image_view"
            android:layout_width="@dimen/_150sdp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:contentDescription="icon" />
    </LinearLayout>
    <!-- CTA Button -->
    <Button
        android:id="@+id/cta_button"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:text="INSTALL"
        android:textAllCaps="true"
        android:textColor="?attr/black"
        android:background="@drawable/white_block_ads"
        android:textStyle="bold"
        android:visibility="gone"
        />

    <LinearLayout
        android:id="@+id/ad_options_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"/>
    <FrameLayout
        android:id="@+id/star_rating_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>
