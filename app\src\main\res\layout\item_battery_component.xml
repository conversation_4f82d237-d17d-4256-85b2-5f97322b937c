<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="88dp"
    android:layout_height="88dp"
    android:layout_marginEnd="4dp"
    android:background="@android:color/transparent"
    android:clickable="true"
    android:focusable="true">

    <!-- Shimmer Loading Layout -->
    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/shimmerLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shimmer_auto_start="true"
        app:shimmer_base_alpha="0.7"
        app:shimmer_direction="left_to_right"
        app:shimmer_duration="1500"
        app:shimmer_highlight_alpha="0.6"
        app:shimmer_shape="linear">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/shimmer_placeholder" />

    </com.facebook.shimmer.ShimmerFrameLayout>

    <!-- Component Container -->
    <androidx.cardview.widget.CardView
        android:id="@+id/componentContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="2dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_component_default"
            android:padding="8dp">

            <!-- Battery Image -->
            <ImageView
                android:id="@+id/batteryImage"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_margin="4dp"
                android:contentDescription="@string/battery_component_description"
                android:scaleType="centerInside"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/placeholder_battery_component" />

            <!-- Selection Indicator -->
            <View
                android:id="@+id/selectionIndicator"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/bg_selection_indicator"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

    <!-- Premium Lock Button -->
    <ImageButton
        android:id="@+id/lockBtn"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="2dp"
        android:background="@drawable/bg_premium_lock"
        android:contentDescription="@string/premium_unlock_description"
        android:padding="2dp"
        android:scaleType="centerInside"
        android:src="@drawable/ic_lock_premium"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/componentContainer"
        app:layout_constraintTop_toTopOf="@id/componentContainer" />

</androidx.constraintlayout.widget.ConstraintLayout>
