// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import me.tankery.lib.circularseekbar.CircularSeekBar;

public final class ItemSlideLayout5Binding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CircularSeekBar circularbar;

  @NonNull
  public final LinearLayout nextPage;

  @NonNull
  public final TextView p11;

  @NonNull
  public final ConstraintLayout p5;

  @NonNull
  public final TextView swipeText;

  @NonNull
  public final SwitchCompat switchInfo;

  @NonNull
  public final TextView textPercent;

  @NonNull
  public final TextView textView4;

  private ItemSlideLayout5Binding(@NonNull LinearLayout rootView,
      @NonNull CircularSeekBar circularbar, @NonNull LinearLayout nextPage, @NonNull TextView p11,
      @NonNull ConstraintLayout p5, @NonNull TextView swipeText, @NonNull SwitchCompat switchInfo,
      @NonNull TextView textPercent, @NonNull TextView textView4) {
    this.rootView = rootView;
    this.circularbar = circularbar;
    this.nextPage = nextPage;
    this.p11 = p11;
    this.p5 = p5;
    this.swipeText = swipeText;
    this.switchInfo = switchInfo;
    this.textPercent = textPercent;
    this.textView4 = textView4;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSlideLayout5Binding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSlideLayout5Binding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_slide_layout_5, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSlideLayout5Binding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.circularbar;
      CircularSeekBar circularbar = ViewBindings.findChildViewById(rootView, id);
      if (circularbar == null) {
        break missingId;
      }

      id = R.id.next_page;
      LinearLayout nextPage = ViewBindings.findChildViewById(rootView, id);
      if (nextPage == null) {
        break missingId;
      }

      id = R.id.p_11;
      TextView p11 = ViewBindings.findChildViewById(rootView, id);
      if (p11 == null) {
        break missingId;
      }

      id = R.id.p5;
      ConstraintLayout p5 = ViewBindings.findChildViewById(rootView, id);
      if (p5 == null) {
        break missingId;
      }

      id = R.id.swipe_text;
      TextView swipeText = ViewBindings.findChildViewById(rootView, id);
      if (swipeText == null) {
        break missingId;
      }

      id = R.id.switch_info;
      SwitchCompat switchInfo = ViewBindings.findChildViewById(rootView, id);
      if (switchInfo == null) {
        break missingId;
      }

      id = R.id.text_percent;
      TextView textPercent = ViewBindings.findChildViewById(rootView, id);
      if (textPercent == null) {
        break missingId;
      }

      id = R.id.textView4;
      TextView textView4 = ViewBindings.findChildViewById(rootView, id);
      if (textView4 == null) {
        break missingId;
      }

      return new ItemSlideLayout5Binding((LinearLayout) rootView, circularbar, nextPage, p11, p5,
          swipeText, switchInfo, textPercent, textView4);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
