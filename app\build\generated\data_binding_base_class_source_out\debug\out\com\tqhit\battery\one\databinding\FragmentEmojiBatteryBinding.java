// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentEmojiBatteryBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final RecyclerView categoryRecyclerView;

  @NonNull
  public final ImageButton clearSearchButton;

  @NonNull
  public final ImageView emojiInfo;

  @NonNull
  public final RecyclerView emojiRecyclerView;

  @NonNull
  public final TextView emojiTitle;

  @NonNull
  public final LinearLayout emptyContainer;

  @NonNull
  public final LinearLayout errorContainer;

  @NonNull
  public final TextView errorMessage;

  @NonNull
  public final ImageButton filterButton;

  @NonNull
  public final Switch globalToggleSwitch;

  @NonNull
  public final FrameLayout loadingContainer;

  @NonNull
  public final ImageButton refreshButton;

  @NonNull
  public final Button retryButton;

  @NonNull
  public final ImageButton searchButton;

  @NonNull
  public final LinearLayout searchContainer;

  @NonNull
  public final EditText searchEditText;

  private FragmentEmojiBatteryBinding(@NonNull ConstraintLayout rootView,
      @NonNull RecyclerView categoryRecyclerView, @NonNull ImageButton clearSearchButton,
      @NonNull ImageView emojiInfo, @NonNull RecyclerView emojiRecyclerView,
      @NonNull TextView emojiTitle, @NonNull LinearLayout emptyContainer,
      @NonNull LinearLayout errorContainer, @NonNull TextView errorMessage,
      @NonNull ImageButton filterButton, @NonNull Switch globalToggleSwitch,
      @NonNull FrameLayout loadingContainer, @NonNull ImageButton refreshButton,
      @NonNull Button retryButton, @NonNull ImageButton searchButton,
      @NonNull LinearLayout searchContainer, @NonNull EditText searchEditText) {
    this.rootView = rootView;
    this.categoryRecyclerView = categoryRecyclerView;
    this.clearSearchButton = clearSearchButton;
    this.emojiInfo = emojiInfo;
    this.emojiRecyclerView = emojiRecyclerView;
    this.emojiTitle = emojiTitle;
    this.emptyContainer = emptyContainer;
    this.errorContainer = errorContainer;
    this.errorMessage = errorMessage;
    this.filterButton = filterButton;
    this.globalToggleSwitch = globalToggleSwitch;
    this.loadingContainer = loadingContainer;
    this.refreshButton = refreshButton;
    this.retryButton = retryButton;
    this.searchButton = searchButton;
    this.searchContainer = searchContainer;
    this.searchEditText = searchEditText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentEmojiBatteryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentEmojiBatteryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_emoji_battery, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentEmojiBatteryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.categoryRecyclerView;
      RecyclerView categoryRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (categoryRecyclerView == null) {
        break missingId;
      }

      id = R.id.clearSearchButton;
      ImageButton clearSearchButton = ViewBindings.findChildViewById(rootView, id);
      if (clearSearchButton == null) {
        break missingId;
      }

      id = R.id.emojiInfo;
      ImageView emojiInfo = ViewBindings.findChildViewById(rootView, id);
      if (emojiInfo == null) {
        break missingId;
      }

      id = R.id.emojiRecyclerView;
      RecyclerView emojiRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (emojiRecyclerView == null) {
        break missingId;
      }

      id = R.id.emojiTitle;
      TextView emojiTitle = ViewBindings.findChildViewById(rootView, id);
      if (emojiTitle == null) {
        break missingId;
      }

      id = R.id.emptyContainer;
      LinearLayout emptyContainer = ViewBindings.findChildViewById(rootView, id);
      if (emptyContainer == null) {
        break missingId;
      }

      id = R.id.errorContainer;
      LinearLayout errorContainer = ViewBindings.findChildViewById(rootView, id);
      if (errorContainer == null) {
        break missingId;
      }

      id = R.id.errorMessage;
      TextView errorMessage = ViewBindings.findChildViewById(rootView, id);
      if (errorMessage == null) {
        break missingId;
      }

      id = R.id.filterButton;
      ImageButton filterButton = ViewBindings.findChildViewById(rootView, id);
      if (filterButton == null) {
        break missingId;
      }

      id = R.id.globalToggleSwitch;
      Switch globalToggleSwitch = ViewBindings.findChildViewById(rootView, id);
      if (globalToggleSwitch == null) {
        break missingId;
      }

      id = R.id.loadingContainer;
      FrameLayout loadingContainer = ViewBindings.findChildViewById(rootView, id);
      if (loadingContainer == null) {
        break missingId;
      }

      id = R.id.refreshButton;
      ImageButton refreshButton = ViewBindings.findChildViewById(rootView, id);
      if (refreshButton == null) {
        break missingId;
      }

      id = R.id.retryButton;
      Button retryButton = ViewBindings.findChildViewById(rootView, id);
      if (retryButton == null) {
        break missingId;
      }

      id = R.id.searchButton;
      ImageButton searchButton = ViewBindings.findChildViewById(rootView, id);
      if (searchButton == null) {
        break missingId;
      }

      id = R.id.searchContainer;
      LinearLayout searchContainer = ViewBindings.findChildViewById(rootView, id);
      if (searchContainer == null) {
        break missingId;
      }

      id = R.id.searchEditText;
      EditText searchEditText = ViewBindings.findChildViewById(rootView, id);
      if (searchEditText == null) {
        break missingId;
      }

      return new FragmentEmojiBatteryBinding((ConstraintLayout) rootView, categoryRecyclerView,
          clearSearchButton, emojiInfo, emojiRecyclerView, emojiTitle, emptyContainer,
          errorContainer, errorMessage, filterButton, globalToggleSwitch, loadingContainer,
          refreshButton, retryButton, searchButton, searchContainer, searchEditText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
