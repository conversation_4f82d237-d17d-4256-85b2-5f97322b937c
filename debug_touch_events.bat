@echo off
echo ========================================
echo TJ_BatteryOne Touch Event Debug Script
echo ========================================
echo.

echo Building debug APK...
call gradlew assembleDebug
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Installing APK...
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %ERRORLEVEL% neq 0 (
    echo Install failed!
    pause
    exit /b 1
)

echo.
echo Starting application...
adb shell am start -W -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.activity.splash.SplashActivity

echo.
echo Starting logcat monitoring for touch events...
echo Press Ctrl+C to stop monitoring
echo.
echo ========================================
echo TOUCH EVENT MONITORING
echo ========================================
echo Look for these tags:
echo - TOUCH_DEBUG: Touch event setup and clicks
echo - BATTERY_COMPONENT_ADAPTER: Battery carousel events
echo - EMOJI_COMPONENT_ADAPTER: Emoji carousel events
echo - EMOJI_CUSTOMIZE_ACTIVITY: Activity-level events
echo ========================================
echo.

adb logcat -s TOUCH_DEBUG:D BATTERY_COMPONENT_ADAPTER:D EMOJI_COMPONENT_ADAPTER:D EMOJI_CUSTOMIZE_ACTIVITY:D

pause
