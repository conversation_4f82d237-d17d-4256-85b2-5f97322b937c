package com.tqhit.battery.one.fragment.main;

import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager;
import com.tqhit.battery.one.features.navigation.AppNavigator;
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository;
import com.tqhit.battery.one.manager.charge.ChargingSessionManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class HealthFragment_MembersInjector implements MembersInjector<HealthFragment> {
  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  private final Provider<ChargingSessionManager> chargingSessionManagerProvider;

  private final Provider<HistoryBatteryRepository> historyBatteryRepositoryProvider;

  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  private final Provider<AppNavigator> appNavigatorProvider;

  private final Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider;

  public HealthFragment_MembersInjector(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<ChargingSessionManager> chargingSessionManagerProvider,
      Provider<HistoryBatteryRepository> historyBatteryRepositoryProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<AppNavigator> appNavigatorProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider) {
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
    this.chargingSessionManagerProvider = chargingSessionManagerProvider;
    this.historyBatteryRepositoryProvider = historyBatteryRepositoryProvider;
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
    this.appNavigatorProvider = appNavigatorProvider;
    this.applovinNativeAdManagerProvider = applovinNativeAdManagerProvider;
  }

  public static MembersInjector<HealthFragment> create(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<ChargingSessionManager> chargingSessionManagerProvider,
      Provider<HistoryBatteryRepository> historyBatteryRepositoryProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<AppNavigator> appNavigatorProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider) {
    return new HealthFragment_MembersInjector(applovinInterstitialAdManagerProvider, chargingSessionManagerProvider, historyBatteryRepositoryProvider, coreBatteryStatsProvider, appNavigatorProvider, applovinNativeAdManagerProvider);
  }

  @Override
  public void injectMembers(HealthFragment instance) {
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
    injectChargingSessionManager(instance, chargingSessionManagerProvider.get());
    injectHistoryBatteryRepository(instance, historyBatteryRepositoryProvider.get());
    injectCoreBatteryStatsProvider(instance, coreBatteryStatsProvider.get());
    injectAppNavigator(instance, appNavigatorProvider.get());
    injectApplovinNativeAdManager(instance, applovinNativeAdManagerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.HealthFragment.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(HealthFragment instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.HealthFragment.chargingSessionManager")
  public static void injectChargingSessionManager(HealthFragment instance,
      ChargingSessionManager chargingSessionManager) {
    instance.chargingSessionManager = chargingSessionManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.HealthFragment.historyBatteryRepository")
  public static void injectHistoryBatteryRepository(HealthFragment instance,
      HistoryBatteryRepository historyBatteryRepository) {
    instance.historyBatteryRepository = historyBatteryRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.HealthFragment.coreBatteryStatsProvider")
  public static void injectCoreBatteryStatsProvider(HealthFragment instance,
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    instance.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.HealthFragment.appNavigator")
  public static void injectAppNavigator(HealthFragment instance, AppNavigator appNavigator) {
    instance.appNavigator = appNavigator;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.HealthFragment.applovinNativeAdManager")
  public static void injectApplovinNativeAdManager(HealthFragment instance,
      ApplovinNativeAdManager applovinNativeAdManager) {
    instance.applovinNativeAdManager = applovinNativeAdManager;
  }
}
