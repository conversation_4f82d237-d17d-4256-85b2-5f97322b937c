package com.tqhit.battery.one.fragment.main;

import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager;
import com.tqhit.battery.one.features.navigation.AppNavigator;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SettingsFragment_MembersInjector implements MembersInjector<SettingsFragment> {
  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  private final Provider<PreferencesHelper> preferencesHelperProvider;

  private final Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider;

  private final Provider<AppNavigator> appNavigatorProvider;

  public SettingsFragment_MembersInjector(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<PreferencesHelper> preferencesHelperProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider,
      Provider<AppNavigator> appNavigatorProvider) {
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
    this.preferencesHelperProvider = preferencesHelperProvider;
    this.applovinNativeAdManagerProvider = applovinNativeAdManagerProvider;
    this.appNavigatorProvider = appNavigatorProvider;
  }

  public static MembersInjector<SettingsFragment> create(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<PreferencesHelper> preferencesHelperProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider,
      Provider<AppNavigator> appNavigatorProvider) {
    return new SettingsFragment_MembersInjector(applovinInterstitialAdManagerProvider, preferencesHelperProvider, applovinNativeAdManagerProvider, appNavigatorProvider);
  }

  @Override
  public void injectMembers(SettingsFragment instance) {
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
    injectPreferencesHelper(instance, preferencesHelperProvider.get());
    injectApplovinNativeAdManager(instance, applovinNativeAdManagerProvider.get());
    injectAppNavigator(instance, appNavigatorProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.SettingsFragment.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(SettingsFragment instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.SettingsFragment.preferencesHelper")
  public static void injectPreferencesHelper(SettingsFragment instance,
      PreferencesHelper preferencesHelper) {
    instance.preferencesHelper = preferencesHelper;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.SettingsFragment.applovinNativeAdManager")
  public static void injectApplovinNativeAdManager(SettingsFragment instance,
      ApplovinNativeAdManager applovinNativeAdManager) {
    instance.applovinNativeAdManager = applovinNativeAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.SettingsFragment.appNavigator")
  public static void injectAppNavigator(SettingsFragment instance, AppNavigator appNavigator) {
    instance.appNavigator = appNavigator;
  }
}
