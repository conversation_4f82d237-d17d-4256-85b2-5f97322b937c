<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/categoryBlock"
    android:focusable="true"
    android:clickable="true"
    android:paddingHorizontal="20dp"
    android:paddingVertical="3dp"
    android:layout_marginEnd="7dp"
    android:background="@drawable/grey_block_line_up"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:foreground="?android:attr/selectableItemBackground">

    <TextView
        android:id="@+id/categoryName"
        android:textSize="14sp"
        android:textColor="?attr/black"
        android:layout_marginVertical="8dp"
        android:gravity="center"
        android:layout_gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/animation"
        android:fontFamily="@font/ubuntu"
        android:textStyle="normal"
        android:letterSpacing="0.01"
        android:includeFontPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>