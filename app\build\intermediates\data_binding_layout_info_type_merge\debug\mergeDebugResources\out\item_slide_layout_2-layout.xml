<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_slide_layout_2" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_slide_layout_2.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_slide_layout_2_0" view="LinearLayout"><Expressions/><location startLine="2" startOffset="0" endLine="133" endOffset="14"/></Target><Target id="@+id/textView4" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="38" endOffset="44"/></Target><Target id="@+id/under_text" view="TextView"><Expressions/><location startLine="39" startOffset="8" endLine="51" endOffset="43"/></Target><Target id="@+id/privacy_policy_button" view="Button"><Expressions/><location startLine="62" startOffset="12" endLine="77" endOffset="66"/></Target><Target id="@+id/confirm_and_continue_button" view="Button"><Expressions/><location startLine="78" startOffset="12" endLine="95" endOffset="66"/></Target><Target id="@+id/next_page" view="LinearLayout"><Expressions/><location startLine="101" startOffset="4" endLine="132" endOffset="18"/></Target><Target id="@+id/swipe_text" view="TextView"><Expressions/><location startLine="116" startOffset="8" endLine="125" endOffset="40"/></Target></Targets></Layout>