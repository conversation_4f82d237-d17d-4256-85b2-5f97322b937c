package com.tqhit.battery.one;

import com.tqhit.adlib.sdk.di.AdmobModule;
import com.tqhit.adlib.sdk.di.AnalyticsModule;
import com.tqhit.adlib.sdk.di.AppModule;
import com.tqhit.adlib.sdk.di.FirebaseModule;
import com.tqhit.adlib.sdk.di.SharedPreferencesModule;
import com.tqhit.adlib.sdk.ui.main.MainActivity_GeneratedInjector;
import com.tqhit.battery.one.activity.animation.AnimationActivity_GeneratedInjector;
import com.tqhit.battery.one.activity.onboarding.LanguageSelectionActivity_GeneratedInjector;
import com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity_GeneratedInjector;
import com.tqhit.battery.one.activity.password.EnterPasswordActivity_GeneratedInjector;
import com.tqhit.battery.one.activity.splash.SplashActivity_GeneratedInjector;
import com.tqhit.battery.one.activity.starting.StartingActivity_GeneratedInjector;
import com.tqhit.battery.one.di.ThumbnailPreloadingModule;
import com.tqhit.battery.one.dialog.utils.ApplovinAdEntryPoint;
import com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule;
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment_GeneratedInjector;
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules;
import com.tqhit.battery.one.features.emoji.presentation.customize.EmojiCustomizeActivity_GeneratedInjector;
import com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules;
import com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment_GeneratedInjector;
import com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService_GeneratedInjector;
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel_HiltModules;
import com.tqhit.battery.one.features.navigation.di.NavigationDIModule;
import com.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment_GeneratedInjector;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules;
import com.tqhit.battery.one.features.stats.corebattery.di.CoreBatteryDIModule;
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService_GeneratedInjector;
import com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeModule;
import com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment_GeneratedInjector;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules;
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService_GeneratedInjector;
import com.tqhit.battery.one.features.stats.health.di.HealthDIModule;
import com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules;
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService_GeneratedInjector;
import com.tqhit.battery.one.fragment.main.HealthFragment_GeneratedInjector;
import com.tqhit.battery.one.fragment.main.SettingsFragment_GeneratedInjector;
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment_GeneratedInjector;
import com.tqhit.battery.one.fragment.main.others.OthersFragment_GeneratedInjector;
import com.tqhit.battery.one.fragment.onboarding.LanguageSelectionViewModel_HiltModules;
import com.tqhit.battery.one.service.ChargingOverlayService_GeneratedInjector;
import com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules;
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules;
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules;
import dagger.Binds;
import dagger.Component;
import dagger.Module;
import dagger.Subcomponent;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.android.components.ActivityRetainedComponent;
import dagger.hilt.android.components.FragmentComponent;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.android.components.ViewComponent;
import dagger.hilt.android.components.ViewModelComponent;
import dagger.hilt.android.components.ViewWithFragmentComponent;
import dagger.hilt.android.flags.FragmentGetContextFix;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.HiltViewModelFactory;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_DefaultViewModelFactories_ActivityModule;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_HiltViewModelFactory_ViewModelModule;
import dagger.hilt.android.internal.managers.ActivityComponentManager;
import dagger.hilt.android.internal.managers.FragmentComponentManager;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_LifecycleModule;
import dagger.hilt.android.internal.managers.HiltWrapper_SavedStateHandleModule;
import dagger.hilt.android.internal.managers.ServiceComponentManager;
import dagger.hilt.android.internal.managers.ViewComponentManager;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.HiltWrapper_ActivityModule;
import dagger.hilt.android.scopes.ActivityRetainedScoped;
import dagger.hilt.android.scopes.ActivityScoped;
import dagger.hilt.android.scopes.FragmentScoped;
import dagger.hilt.android.scopes.ServiceScoped;
import dagger.hilt.android.scopes.ViewModelScoped;
import dagger.hilt.android.scopes.ViewScoped;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedComponent;
import dagger.hilt.migration.DisableInstallInCheck;
import javax.annotation.processing.Generated;
import javax.inject.Singleton;

@Generated("dagger.hilt.processor.internal.root.RootProcessor")
public final class BatteryApplication_HiltComponents {
  private BatteryApplication_HiltComponents() {
  }

  @Module(
      subcomponents = ServiceC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ServiceCBuilderModule {
    @Binds
    ServiceComponentBuilder bind(ServiceC.Builder builder);
  }

  @Module(
      subcomponents = ActivityRetainedC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ActivityRetainedCBuilderModule {
    @Binds
    ActivityRetainedComponentBuilder bind(ActivityRetainedC.Builder builder);
  }

  @Module(
      subcomponents = ActivityC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ActivityCBuilderModule {
    @Binds
    ActivityComponentBuilder bind(ActivityC.Builder builder);
  }

  @Module(
      subcomponents = ViewModelC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ViewModelCBuilderModule {
    @Binds
    ViewModelComponentBuilder bind(ViewModelC.Builder builder);
  }

  @Module(
      subcomponents = ViewC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ViewCBuilderModule {
    @Binds
    ViewComponentBuilder bind(ViewC.Builder builder);
  }

  @Module(
      subcomponents = FragmentC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface FragmentCBuilderModule {
    @Binds
    FragmentComponentBuilder bind(FragmentC.Builder builder);
  }

  @Module(
      subcomponents = ViewWithFragmentC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ViewWithFragmentCBuilderModule {
    @Binds
    ViewWithFragmentComponentBuilder bind(ViewWithFragmentC.Builder builder);
  }

  @Component(
      modules = {
          AdmobModule.class,
          AnalyticsModule.class,
          AppModule.class,
          ApplicationContextModule.class,
          ActivityRetainedCBuilderModule.class,
          ServiceCBuilderModule.class,
          CoreBatteryDIModule.class,
          EmojiBatteryDIModule.class,
          FirebaseModule.class,
          HealthDIModule.class,
          HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule.class,
          NavigationDIModule.class,
          SharedPreferencesModule.class,
          StatsChargeDIModule.class,
          StatsDischargeModule.class,
          StatsDischargeProvidersModule.class,
          ThumbnailPreloadingModule.class
      }
  )
  @Singleton
  public abstract static class SingletonC implements BatteryApplication_GeneratedInjector,
      ApplovinAdEntryPoint,
      FragmentGetContextFix.FragmentGetContextFixEntryPoint,
      HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint,
      ServiceComponentManager.ServiceComponentBuilderEntryPoint,
      SingletonComponent,
      GeneratedComponent {
  }

  @Subcomponent
  @ServiceScoped
  public abstract static class ServiceC implements EmojiBatteryAccessibilityService_GeneratedInjector,
      CoreBatteryStatsService_GeneratedInjector,
      EnhancedDischargeTimerService_GeneratedInjector,
      UnifiedBatteryNotificationService_GeneratedInjector,
      ChargingOverlayService_GeneratedInjector,
      ServiceComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ServiceComponentBuilder {
    }
  }

  @Subcomponent(
      modules = {
          AnimationViewModel_HiltModules.KeyModule.class,
          AppViewModel_HiltModules.KeyModule.class,
          ActivityCBuilderModule.class,
          ViewModelCBuilderModule.class,
          BatteryGalleryViewModel_HiltModules.KeyModule.class,
          BatteryViewModel_HiltModules.KeyModule.class,
          CustomizeViewModel_HiltModules.KeyModule.class,
          DischargeViewModel_HiltModules.KeyModule.class,
          HealthViewModel_HiltModules.KeyModule.class,
          HiltWrapper_ActivityRetainedComponentManager_LifecycleModule.class,
          HiltWrapper_SavedStateHandleModule.class,
          LanguageSelectionViewModel_HiltModules.KeyModule.class,
          SharedNavigationViewModel_HiltModules.KeyModule.class,
          StatsChargeViewModel_HiltModules.KeyModule.class
      }
  )
  @ActivityRetainedScoped
  public abstract static class ActivityRetainedC implements ActivityRetainedComponent,
      ActivityComponentManager.ActivityComponentBuilderEntryPoint,
      HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ActivityRetainedComponentBuilder {
    }
  }

  @Subcomponent(
      modules = {
          FragmentCBuilderModule.class,
          ViewCBuilderModule.class,
          HiltWrapper_ActivityModule.class,
          HiltWrapper_DefaultViewModelFactories_ActivityModule.class
      }
  )
  @ActivityScoped
  public abstract static class ActivityC implements MainActivity_GeneratedInjector,
      AnimationActivity_GeneratedInjector,
      com.tqhit.battery.one.activity.main.MainActivity_GeneratedInjector,
      LanguageSelectionActivity_GeneratedInjector,
      ChargingOverlayActivity_GeneratedInjector,
      EnterPasswordActivity_GeneratedInjector,
      SplashActivity_GeneratedInjector,
      StartingActivity_GeneratedInjector,
      EmojiCustomizeActivity_GeneratedInjector,
      ActivityComponent,
      DefaultViewModelFactories.ActivityEntryPoint,
      HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint,
      FragmentComponentManager.FragmentComponentBuilderEntryPoint,
      ViewComponentManager.ViewComponentBuilderEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ActivityComponentBuilder {
    }
  }

  @Subcomponent(
      modules = {
          AnimationViewModel_HiltModules.BindsModule.class,
          AppViewModel_HiltModules.BindsModule.class,
          BatteryGalleryViewModel_HiltModules.BindsModule.class,
          BatteryViewModel_HiltModules.BindsModule.class,
          CustomizeViewModel_HiltModules.BindsModule.class,
          DischargeViewModel_HiltModules.BindsModule.class,
          HealthViewModel_HiltModules.BindsModule.class,
          HiltWrapper_HiltViewModelFactory_ViewModelModule.class,
          LanguageSelectionViewModel_HiltModules.BindsModule.class,
          SharedNavigationViewModel_HiltModules.BindsModule.class,
          StatsChargeViewModel_HiltModules.BindsModule.class
      }
  )
  @ViewModelScoped
  public abstract static class ViewModelC implements ViewModelComponent,
      HiltViewModelFactory.ViewModelFactoriesEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewModelComponentBuilder {
    }
  }

  @Subcomponent
  @ViewScoped
  public abstract static class ViewC implements ViewComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewComponentBuilder {
    }
  }

  @Subcomponent(
      modules = ViewWithFragmentCBuilderModule.class
  )
  @FragmentScoped
  public abstract static class FragmentC implements CustomizeFragment_GeneratedInjector,
      EmojiBatteryFragment_GeneratedInjector,
      StatsChargeFragment_GeneratedInjector,
      DischargeFragment_GeneratedInjector,
      HealthFragment_GeneratedInjector,
      SettingsFragment_GeneratedInjector,
      AnimationGridFragment_GeneratedInjector,
      OthersFragment_GeneratedInjector,
      FragmentComponent,
      DefaultViewModelFactories.FragmentEntryPoint,
      ViewComponentManager.ViewWithFragmentComponentBuilderEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends FragmentComponentBuilder {
    }
  }

  @Subcomponent
  @ViewScoped
  public abstract static class ViewWithFragmentC implements ViewWithFragmentComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewWithFragmentComponentBuilder {
    }
  }
}
