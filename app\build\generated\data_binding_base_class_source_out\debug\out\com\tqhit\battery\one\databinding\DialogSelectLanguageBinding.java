// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogSelectLanguageBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final TextView ar;

  @NonNull
  public final TextView de;

  @NonNull
  public final TextView en;

  @NonNull
  public final TextView es;

  @NonNull
  public final Button exit;

  @NonNull
  public final TextView fr;

  @NonNull
  public final TextView hu;

  @NonNull
  public final TextView it;

  @NonNull
  public final ShimmerFrameLayout nativeAd;

  @NonNull
  public final TextView nl;

  @NonNull
  public final TextView pl;

  @NonNull
  public final TextView pt;

  @NonNull
  public final TextView ro;

  @NonNull
  public final TextView ru;

  @NonNull
  public final Button selectButton;

  @NonNull
  public final RelativeLayout strelka;

  @NonNull
  public final TextView textView20;

  @NonNull
  public final TextView tr;

  @NonNull
  public final TextView ua;

  @NonNull
  public final TextView zh;

  private DialogSelectLanguageBinding(@NonNull RelativeLayout rootView, @NonNull TextView ar,
      @NonNull TextView de, @NonNull TextView en, @NonNull TextView es, @NonNull Button exit,
      @NonNull TextView fr, @NonNull TextView hu, @NonNull TextView it,
      @NonNull ShimmerFrameLayout nativeAd, @NonNull TextView nl, @NonNull TextView pl,
      @NonNull TextView pt, @NonNull TextView ro, @NonNull TextView ru,
      @NonNull Button selectButton, @NonNull RelativeLayout strelka, @NonNull TextView textView20,
      @NonNull TextView tr, @NonNull TextView ua, @NonNull TextView zh) {
    this.rootView = rootView;
    this.ar = ar;
    this.de = de;
    this.en = en;
    this.es = es;
    this.exit = exit;
    this.fr = fr;
    this.hu = hu;
    this.it = it;
    this.nativeAd = nativeAd;
    this.nl = nl;
    this.pl = pl;
    this.pt = pt;
    this.ro = ro;
    this.ru = ru;
    this.selectButton = selectButton;
    this.strelka = strelka;
    this.textView20 = textView20;
    this.tr = tr;
    this.ua = ua;
    this.zh = zh;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSelectLanguageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSelectLanguageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_select_language, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSelectLanguageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ar;
      TextView ar = ViewBindings.findChildViewById(rootView, id);
      if (ar == null) {
        break missingId;
      }

      id = R.id.de;
      TextView de = ViewBindings.findChildViewById(rootView, id);
      if (de == null) {
        break missingId;
      }

      id = R.id.en;
      TextView en = ViewBindings.findChildViewById(rootView, id);
      if (en == null) {
        break missingId;
      }

      id = R.id.es;
      TextView es = ViewBindings.findChildViewById(rootView, id);
      if (es == null) {
        break missingId;
      }

      id = R.id.exit;
      Button exit = ViewBindings.findChildViewById(rootView, id);
      if (exit == null) {
        break missingId;
      }

      id = R.id.fr;
      TextView fr = ViewBindings.findChildViewById(rootView, id);
      if (fr == null) {
        break missingId;
      }

      id = R.id.hu;
      TextView hu = ViewBindings.findChildViewById(rootView, id);
      if (hu == null) {
        break missingId;
      }

      id = R.id.it;
      TextView it = ViewBindings.findChildViewById(rootView, id);
      if (it == null) {
        break missingId;
      }

      id = R.id.nativeAd;
      ShimmerFrameLayout nativeAd = ViewBindings.findChildViewById(rootView, id);
      if (nativeAd == null) {
        break missingId;
      }

      id = R.id.nl;
      TextView nl = ViewBindings.findChildViewById(rootView, id);
      if (nl == null) {
        break missingId;
      }

      id = R.id.pl;
      TextView pl = ViewBindings.findChildViewById(rootView, id);
      if (pl == null) {
        break missingId;
      }

      id = R.id.pt;
      TextView pt = ViewBindings.findChildViewById(rootView, id);
      if (pt == null) {
        break missingId;
      }

      id = R.id.ro;
      TextView ro = ViewBindings.findChildViewById(rootView, id);
      if (ro == null) {
        break missingId;
      }

      id = R.id.ru;
      TextView ru = ViewBindings.findChildViewById(rootView, id);
      if (ru == null) {
        break missingId;
      }

      id = R.id.select_button;
      Button selectButton = ViewBindings.findChildViewById(rootView, id);
      if (selectButton == null) {
        break missingId;
      }

      id = R.id.strelka;
      RelativeLayout strelka = ViewBindings.findChildViewById(rootView, id);
      if (strelka == null) {
        break missingId;
      }

      id = R.id.textView20;
      TextView textView20 = ViewBindings.findChildViewById(rootView, id);
      if (textView20 == null) {
        break missingId;
      }

      id = R.id.tr;
      TextView tr = ViewBindings.findChildViewById(rootView, id);
      if (tr == null) {
        break missingId;
      }

      id = R.id.ua;
      TextView ua = ViewBindings.findChildViewById(rootView, id);
      if (ua == null) {
        break missingId;
      }

      id = R.id.zh;
      TextView zh = ViewBindings.findChildViewById(rootView, id);
      if (zh == null) {
        break missingId;
      }

      return new DialogSelectLanguageBinding((RelativeLayout) rootView, ar, de, en, es, exit, fr,
          hu, it, nativeAd, nl, pl, pt, ro, ru, selectButton, strelka, textView20, tr, ua, zh);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
