package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ActivityRetainedComponent",
    modules = "com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules.KeyModule"
)
@Generated("dagger.hilt.processor.internal.aggregateddeps.AggregatedDepsGenerator")
public class _com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_KeyModule {
}
