package com.tqhit.battery.one.features.stats.discharge.presentation;

import com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter;
import com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository;
import com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository;
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DischargeViewModel_Factory implements Factory<DischargeViewModel> {
  private final Provider<BatteryRepository> batteryRepositoryProvider;

  private final Provider<DischargeCalculator> calculatorProvider;

  private final Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider;

  private final Provider<TimeConverter> timeConverterProvider;

  private final Provider<EnhancedDischargeTimerServiceHelper> enhancedDischargeTimerServiceHelperProvider;

  public DischargeViewModel_Factory(Provider<BatteryRepository> batteryRepositoryProvider,
      Provider<DischargeCalculator> calculatorProvider,
      Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider,
      Provider<TimeConverter> timeConverterProvider,
      Provider<EnhancedDischargeTimerServiceHelper> enhancedDischargeTimerServiceHelperProvider) {
    this.batteryRepositoryProvider = batteryRepositoryProvider;
    this.calculatorProvider = calculatorProvider;
    this.dischargeSessionRepositoryProvider = dischargeSessionRepositoryProvider;
    this.timeConverterProvider = timeConverterProvider;
    this.enhancedDischargeTimerServiceHelperProvider = enhancedDischargeTimerServiceHelperProvider;
  }

  @Override
  public DischargeViewModel get() {
    return newInstance(batteryRepositoryProvider.get(), calculatorProvider.get(), dischargeSessionRepositoryProvider.get(), timeConverterProvider.get(), enhancedDischargeTimerServiceHelperProvider.get());
  }

  public static DischargeViewModel_Factory create(
      Provider<BatteryRepository> batteryRepositoryProvider,
      Provider<DischargeCalculator> calculatorProvider,
      Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider,
      Provider<TimeConverter> timeConverterProvider,
      Provider<EnhancedDischargeTimerServiceHelper> enhancedDischargeTimerServiceHelperProvider) {
    return new DischargeViewModel_Factory(batteryRepositoryProvider, calculatorProvider, dischargeSessionRepositoryProvider, timeConverterProvider, enhancedDischargeTimerServiceHelperProvider);
  }

  public static DischargeViewModel newInstance(BatteryRepository batteryRepository,
      DischargeCalculator calculator, DischargeSessionRepository dischargeSessionRepository,
      TimeConverter timeConverter,
      EnhancedDischargeTimerServiceHelper enhancedDischargeTimerServiceHelper) {
    return new DischargeViewModel(batteryRepository, calculator, dischargeSessionRepository, timeConverter, enhancedDischargeTimerServiceHelper);
  }
}
