// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.google.android.material.imageview.ShapeableImageView;
import com.tqhit.battery.one.R;
import com.tqhit.battery.one.ui.custom.SquareCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBatteryStyleBinding implements ViewBinding {
  @NonNull
  private final SquareCardView rootView;

  @NonNull
  public final TextView categoryBadge;

  @NonNull
  public final TextView lockBtn;

  @NonNull
  public final ImageView popularIndicator;

  @NonNull
  public final ImageView selectionCheckMark;

  @NonNull
  public final View selectionOverlay;

  @NonNull
  public final ShimmerFrameLayout shimmerLayout;

  @NonNull
  public final ShapeableImageView styleImage;

  @NonNull
  public final TextView styleName;

  private ItemBatteryStyleBinding(@NonNull SquareCardView rootView, @NonNull TextView categoryBadge,
      @NonNull TextView lockBtn, @NonNull ImageView popularIndicator,
      @NonNull ImageView selectionCheckMark, @NonNull View selectionOverlay,
      @NonNull ShimmerFrameLayout shimmerLayout, @NonNull ShapeableImageView styleImage,
      @NonNull TextView styleName) {
    this.rootView = rootView;
    this.categoryBadge = categoryBadge;
    this.lockBtn = lockBtn;
    this.popularIndicator = popularIndicator;
    this.selectionCheckMark = selectionCheckMark;
    this.selectionOverlay = selectionOverlay;
    this.shimmerLayout = shimmerLayout;
    this.styleImage = styleImage;
    this.styleName = styleName;
  }

  @Override
  @NonNull
  public SquareCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBatteryStyleBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBatteryStyleBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_battery_style, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBatteryStyleBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.categoryBadge;
      TextView categoryBadge = ViewBindings.findChildViewById(rootView, id);
      if (categoryBadge == null) {
        break missingId;
      }

      id = R.id.lockBtn;
      TextView lockBtn = ViewBindings.findChildViewById(rootView, id);
      if (lockBtn == null) {
        break missingId;
      }

      id = R.id.popularIndicator;
      ImageView popularIndicator = ViewBindings.findChildViewById(rootView, id);
      if (popularIndicator == null) {
        break missingId;
      }

      id = R.id.selectionCheckMark;
      ImageView selectionCheckMark = ViewBindings.findChildViewById(rootView, id);
      if (selectionCheckMark == null) {
        break missingId;
      }

      id = R.id.selectionOverlay;
      View selectionOverlay = ViewBindings.findChildViewById(rootView, id);
      if (selectionOverlay == null) {
        break missingId;
      }

      id = R.id.shimmerLayout;
      ShimmerFrameLayout shimmerLayout = ViewBindings.findChildViewById(rootView, id);
      if (shimmerLayout == null) {
        break missingId;
      }

      id = R.id.styleImage;
      ShapeableImageView styleImage = ViewBindings.findChildViewById(rootView, id);
      if (styleImage == null) {
        break missingId;
      }

      id = R.id.styleName;
      TextView styleName = ViewBindings.findChildViewById(rootView, id);
      if (styleName == null) {
        break missingId;
      }

      return new ItemBatteryStyleBinding((SquareCardView) rootView, categoryBadge, lockBtn,
          popularIndicator, selectionCheckMark, selectionOverlay, shimmerLayout, styleImage,
          styleName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
