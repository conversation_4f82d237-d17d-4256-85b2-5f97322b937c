package com.tqhit.battery.one.features.emoji.domain.use_case;

import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class LoadCustomizationUseCase_Factory implements Factory<LoadCustomizationUseCase> {
  private final Provider<CustomizationRepository> customizationRepositoryProvider;

  public LoadCustomizationUseCase_Factory(
      Provider<CustomizationRepository> customizationRepositoryProvider) {
    this.customizationRepositoryProvider = customizationRepositoryProvider;
  }

  @Override
  public LoadCustomizationUseCase get() {
    return newInstance(customizationRepositoryProvider.get());
  }

  public static LoadCustomizationUseCase_Factory create(
      Provider<CustomizationRepository> customizationRepositoryProvider) {
    return new LoadCustomizationUseCase_Factory(customizationRepositoryProvider);
  }

  public static LoadCustomizationUseCase newInstance(
      CustomizationRepository customizationRepository) {
    return new LoadCustomizationUseCase(customizationRepository);
  }
}
