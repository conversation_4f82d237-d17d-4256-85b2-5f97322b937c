package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.tqhit.battery.one.BatteryApplication",
    rootPackage = "com.tqhit.battery.one",
    originatingRoot = "com.tqhit.battery.one.BatteryApplication",
    originatingRootPackage = "com.tqhit.battery.one",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "BatteryApplication",
    originatingRootSimpleNames = "BatteryApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_tqhit_battery_one_BatteryApplication {
}
