package com.tqhit.battery.one.service;

import android.content.Context;
import com.tqhit.battery.one.repository.AnimationRepository;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ChargingOverlayServiceHelper_Factory implements Factory<ChargingOverlayServiceHelper> {
  private final Provider<Context> contextProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  private final Provider<AnimationRepository> animationRepositoryProvider;

  public ChargingOverlayServiceHelper_Factory(Provider<Context> contextProvider,
      Provider<AppRepository> appRepositoryProvider,
      Provider<AnimationRepository> animationRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.appRepositoryProvider = appRepositoryProvider;
    this.animationRepositoryProvider = animationRepositoryProvider;
  }

  @Override
  public ChargingOverlayServiceHelper get() {
    return newInstance(contextProvider.get(), appRepositoryProvider.get(), animationRepositoryProvider.get());
  }

  public static ChargingOverlayServiceHelper_Factory create(Provider<Context> contextProvider,
      Provider<AppRepository> appRepositoryProvider,
      Provider<AnimationRepository> animationRepositoryProvider) {
    return new ChargingOverlayServiceHelper_Factory(contextProvider, appRepositoryProvider, animationRepositoryProvider);
  }

  public static ChargingOverlayServiceHelper newInstance(Context context,
      AppRepository appRepository, AnimationRepository animationRepository) {
    return new ChargingOverlayServiceHelper(context, appRepository, animationRepository);
  }
}
