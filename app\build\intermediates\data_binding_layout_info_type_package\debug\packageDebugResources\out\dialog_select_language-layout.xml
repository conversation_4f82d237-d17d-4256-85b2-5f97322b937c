<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_select_language" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\dialog_select_language.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/dialog_select_language_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="349" endOffset="16"/></Target><Target id="@+id/strelka" view="RelativeLayout"><Expressions/><location startLine="29" startOffset="16" endLine="61" endOffset="32"/></Target><Target id="@+id/exit" view="Button"><Expressions/><location startLine="36" startOffset="20" endLine="47" endOffset="74"/></Target><Target id="@+id/textView20" view="TextView"><Expressions/><location startLine="62" startOffset="16" endLine="79" endOffset="59"/></Target><Target id="@+id/de" view="TextView"><Expressions/><location startLine="90" startOffset="20" endLine="102" endOffset="57"/></Target><Target id="@+id/nl" view="TextView"><Expressions/><location startLine="103" startOffset="20" endLine="118" endOffset="57"/></Target><Target id="@+id/en" view="TextView"><Expressions/><location startLine="119" startOffset="20" endLine="134" endOffset="57"/></Target><Target id="@+id/es" view="TextView"><Expressions/><location startLine="135" startOffset="20" endLine="150" endOffset="57"/></Target><Target id="@+id/fr" view="TextView"><Expressions/><location startLine="151" startOffset="20" endLine="166" endOffset="57"/></Target><Target id="@+id/it" view="TextView"><Expressions/><location startLine="167" startOffset="20" endLine="182" endOffset="57"/></Target><Target id="@+id/hu" view="TextView"><Expressions/><location startLine="183" startOffset="20" endLine="198" endOffset="57"/></Target><Target id="@+id/pl" view="TextView"><Expressions/><location startLine="199" startOffset="20" endLine="214" endOffset="57"/></Target><Target id="@+id/pt" view="TextView"><Expressions/><location startLine="215" startOffset="20" endLine="230" endOffset="57"/></Target><Target id="@+id/ro" view="TextView"><Expressions/><location startLine="231" startOffset="20" endLine="246" endOffset="57"/></Target><Target id="@+id/tr" view="TextView"><Expressions/><location startLine="247" startOffset="20" endLine="262" endOffset="57"/></Target><Target id="@+id/ru" view="TextView"><Expressions/><location startLine="263" startOffset="20" endLine="278" endOffset="57"/></Target><Target id="@+id/ua" view="TextView"><Expressions/><location startLine="279" startOffset="20" endLine="294" endOffset="57"/></Target><Target id="@+id/ar" view="TextView"><Expressions/><location startLine="295" startOffset="20" endLine="310" endOffset="57"/></Target><Target id="@+id/zh" view="TextView"><Expressions/><location startLine="311" startOffset="20" endLine="325" endOffset="57"/></Target><Target id="@+id/select_button" view="Button"><Expressions/><location startLine="328" startOffset="12" endLine="337" endOffset="41"/></Target><Target id="@+id/nativeAd" view="com.facebook.shimmer.ShimmerFrameLayout"><Expressions/><location startLine="338" startOffset="12" endLine="342" endOffset="49"/></Target></Targets></Layout>