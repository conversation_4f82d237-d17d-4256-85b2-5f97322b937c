package com.tqhit.battery.one.activity.overlay;

import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ChargingOverlayActivity_MembersInjector implements MembersInjector<ChargingOverlayActivity> {
  private final Provider<AppRepository> appRepositoryProvider;

  public ChargingOverlayActivity_MembersInjector(Provider<AppRepository> appRepositoryProvider) {
    this.appRepositoryProvider = appRepositoryProvider;
  }

  public static MembersInjector<ChargingOverlayActivity> create(
      Provider<AppRepository> appRepositoryProvider) {
    return new ChargingOverlayActivity_MembersInjector(appRepositoryProvider);
  }

  @Override
  public void injectMembers(ChargingOverlayActivity instance) {
    injectAppRepository(instance, appRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity.appRepository")
  public static void injectAppRepository(ChargingOverlayActivity instance,
      AppRepository appRepository) {
    instance.appRepository = appRepository;
  }
}
