<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main_l"><Targets><Target id="@+id/main_l" tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="111" endOffset="51"/></Target><Target id="@+id/update_view" view="LinearLayout"><Expressions/><location startLine="6" startOffset="4" endLine="53" endOffset="18"/></Target><Target id="@+id/update_view_text" view="TextView"><Expressions/><location startLine="17" startOffset="8" endLine="32" endOffset="38"/></Target><Target id="@+id/update_view_btn" view="TextView"><Expressions/><location startLine="33" startOffset="8" endLine="52" endOffset="43"/></Target><Target id="@+id/nav_host_fragment" view="FrameLayout"><Expressions/><location startLine="54" startOffset="4" endLine="61" endOffset="68"/></Target><Target id="@+id/ads_baner_layout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="62" startOffset="4" endLine="70" endOffset="54"/></Target><Target id="@+id/panel" view="LinearLayout"><Expressions/><location startLine="71" startOffset="4" endLine="100" endOffset="18"/></Target><Target id="@+id/bottom_view" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="85" startOffset="8" endLine="99" endOffset="39"/></Target><Target id="@+id/banner_container" view="com.applovin.mediation.ads.MaxAdView"><Expressions/><location startLine="101" startOffset="4" endLine="110" endOffset="50"/></Target></Targets></Layout>