package com.tqhit.battery.one.features.emoji.presentation.gallery;

import android.content.Context;
import com.tqhit.battery.one.features.emoji.data.service.EmojiCategoryService;
import com.tqhit.battery.one.features.emoji.data.service.EmojiItemService;
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class BatteryGalleryViewModel_Factory implements Factory<BatteryGalleryViewModel> {
  private final Provider<Context> contextProvider;

  private final Provider<EmojiCategoryService> emojiCategoryServiceProvider;

  private final Provider<EmojiItemService> emojiItemServiceProvider;

  private final Provider<CustomizationRepository> customizationRepositoryProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  public BatteryGalleryViewModel_Factory(Provider<Context> contextProvider,
      Provider<EmojiCategoryService> emojiCategoryServiceProvider,
      Provider<EmojiItemService> emojiItemServiceProvider,
      Provider<CustomizationRepository> customizationRepositoryProvider,
      Provider<AppRepository> appRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.emojiCategoryServiceProvider = emojiCategoryServiceProvider;
    this.emojiItemServiceProvider = emojiItemServiceProvider;
    this.customizationRepositoryProvider = customizationRepositoryProvider;
    this.appRepositoryProvider = appRepositoryProvider;
  }

  @Override
  public BatteryGalleryViewModel get() {
    return newInstance(contextProvider.get(), emojiCategoryServiceProvider.get(), emojiItemServiceProvider.get(), customizationRepositoryProvider.get(), appRepositoryProvider.get());
  }

  public static BatteryGalleryViewModel_Factory create(Provider<Context> contextProvider,
      Provider<EmojiCategoryService> emojiCategoryServiceProvider,
      Provider<EmojiItemService> emojiItemServiceProvider,
      Provider<CustomizationRepository> customizationRepositoryProvider,
      Provider<AppRepository> appRepositoryProvider) {
    return new BatteryGalleryViewModel_Factory(contextProvider, emojiCategoryServiceProvider, emojiItemServiceProvider, customizationRepositoryProvider, appRepositoryProvider);
  }

  public static BatteryGalleryViewModel newInstance(Context context,
      EmojiCategoryService emojiCategoryService, EmojiItemService emojiItemService,
      CustomizationRepository customizationRepository, AppRepository appRepository) {
    return new BatteryGalleryViewModel(context, emojiCategoryService, emojiItemService, customizationRepository, appRepository);
  }
}
