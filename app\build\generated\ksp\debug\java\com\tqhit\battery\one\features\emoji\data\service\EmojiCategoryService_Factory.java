package com.tqhit.battery.one.features.emoji.data.service;

import com.google.gson.Gson;
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class EmojiCategoryService_Factory implements Factory<EmojiCategoryService> {
  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  private final Provider<Gson> gsonProvider;

  public EmojiCategoryService_Factory(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<Gson> gsonProvider) {
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public EmojiCategoryService get() {
    return newInstance(remoteConfigHelperProvider.get(), gsonProvider.get());
  }

  public static EmojiCategoryService_Factory create(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<Gson> gsonProvider) {
    return new EmojiCategoryService_Factory(remoteConfigHelperProvider, gsonProvider);
  }

  public static EmojiCategoryService newInstance(FirebaseRemoteConfigHelper remoteConfigHelper,
      Gson gson) {
    return new EmojiCategoryService(remoteConfigHelper, gson);
  }
}
