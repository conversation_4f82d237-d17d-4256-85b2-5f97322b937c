package com.tqhit.battery.one.features.emoji.presentation.overlay;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;
import javax.annotation.processing.Generated;

@OriginatingElement(
    topLevelClass = EmojiBatteryAccessibilityService.class
)
@GeneratedEntryPoint
@InstallIn(ServiceComponent.class)
@Generated("dagger.hilt.android.processor.internal.androidentrypoint.InjectorEntryPointGenerator")
public interface EmojiBatteryAccessibilityService_GeneratedInjector {
  void injectEmojiBatteryAccessibilityService(
      EmojiBatteryAccessibilityService emojiBatteryAccessibilityService);
}
