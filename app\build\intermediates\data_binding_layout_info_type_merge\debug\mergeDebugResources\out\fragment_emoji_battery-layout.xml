<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_emoji_battery" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_emoji_battery.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_emoji_battery_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="246" endOffset="51"/></Target><Target id="@+id/emojiTitle" view="TextView"><Expressions/><location startLine="30" startOffset="12" endLine="38" endOffset="41"/></Target><Target id="@+id/emojiInfo" view="ImageView"><Expressions/><location startLine="40" startOffset="12" endLine="47" endOffset="46"/></Target><Target id="@+id/globalToggleSwitch" view="Switch"><Expressions/><location startLine="67" startOffset="12" endLine="71" endOffset="41"/></Target><Target id="@+id/searchContainer" view="LinearLayout"><Expressions/><location startLine="75" startOffset="8" endLine="102" endOffset="22"/></Target><Target id="@+id/searchEditText" view="EditText"><Expressions/><location startLine="83" startOffset="12" endLine="92" endOffset="40"/></Target><Target id="@+id/clearSearchButton" view="ImageButton"><Expressions/><location startLine="94" startOffset="12" endLine="101" endOffset="67"/></Target><Target id="@+id/searchButton" view="ImageButton"><Expressions/><location startLine="111" startOffset="12" endLine="117" endOffset="61"/></Target><Target id="@+id/filterButton" view="ImageButton"><Expressions/><location startLine="124" startOffset="12" endLine="130" endOffset="61"/></Target><Target id="@+id/refreshButton" view="ImageButton"><Expressions/><location startLine="132" startOffset="12" endLine="138" endOffset="62"/></Target><Target id="@+id/categoryRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="142" startOffset="8" endLine="150" endOffset="82"/></Target><Target id="@+id/loadingContainer" view="FrameLayout"><Expressions/><location startLine="153" startOffset="8" endLine="173" endOffset="21"/></Target><Target id="@+id/errorContainer" view="LinearLayout"><Expressions/><location startLine="176" startOffset="8" endLine="207" endOffset="22"/></Target><Target id="@+id/errorMessage" view="TextView"><Expressions/><location startLine="191" startOffset="12" endLine="199" endOffset="41"/></Target><Target id="@+id/retryButton" view="Button"><Expressions/><location startLine="201" startOffset="12" endLine="206" endOffset="46"/></Target><Target id="@+id/emptyContainer" view="LinearLayout"><Expressions/><location startLine="210" startOffset="8" endLine="233" endOffset="22"/></Target><Target id="@+id/emojiRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="236" startOffset="8" endLine="241" endOffset="43"/></Target></Targets></Layout>